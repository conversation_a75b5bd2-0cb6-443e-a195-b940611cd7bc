import {
  calculateCAGR,
  calculateAbsoluteReturn,
  calculateYearsBetweenDates,
  formatCurrency,
  formatPercentage,
  formatIndianNumber,
  validateDateRange,
  generateId,
  isMarketOpen,
  safeJsonParse,
} from '../index';

describe('Utility Functions', () => {
  describe('calculateCAGR', () => {
    it('should calculate CAGR correctly', () => {
      const result = calculateCAGR(100000, 150000, 2);
      expect(result).toBe(22.47); // (150000/100000)^(1/2) - 1 = 0.2247
    });

    it('should return 0 for invalid inputs', () => {
      expect(calculateCAGR(0, 150000, 2)).toBe(0);
      expect(calculateCAGR(100000, 0, 2)).toBe(0);
      expect(calculateCAGR(100000, 150000, 0)).toBe(0);
    });

    it('should handle negative values correctly', () => {
      expect(calculateCAGR(-100000, 150000, 2)).toBe(0);
    });
  });

  describe('calculateAbsoluteReturn', () => {
    it('should calculate absolute return correctly', () => {
      const result = calculateAbsoluteReturn(100000, 150000);
      expect(result).toBe(50.00);
    });

    it('should handle losses correctly', () => {
      const result = calculateAbsoluteReturn(100000, 80000);
      expect(result).toBe(-20.00);
    });

    it('should return 0 for zero initial value', () => {
      expect(calculateAbsoluteReturn(0, 150000)).toBe(0);
    });
  });

  describe('calculateYearsBetweenDates', () => {
    it('should calculate years correctly', () => {
      const startDate = new Date('2020-01-01');
      const endDate = new Date('2022-01-01');
      const result = calculateYearsBetweenDates(startDate, endDate);
      expect(result).toBeCloseTo(2, 1);
    });

    it('should handle partial years', () => {
      const startDate = new Date('2020-01-01');
      const endDate = new Date('2020-07-01');
      const result = calculateYearsBetweenDates(startDate, endDate);
      expect(result).toBeCloseTo(0.5, 1);
    });
  });

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      const result = formatCurrency(100000);
      expect(result).toContain('₹');
      expect(result).toContain('1,00,000');
    });

    it('should handle decimal values', () => {
      const result = formatCurrency(100000.50);
      expect(result).toContain('₹');
      expect(result).toContain('.50');
    });
  });

  describe('formatPercentage', () => {
    it('should format percentage correctly', () => {
      expect(formatPercentage(25.5)).toBe('25.50%');
      expect(formatPercentage(0)).toBe('0.00%');
      expect(formatPercentage(-10.25)).toBe('-10.25%');
    });
  });

  describe('formatIndianNumber', () => {
    it('should format numbers in Indian style', () => {
      expect(formatIndianNumber(100000)).toBe('1,00,000');
      expect(formatIndianNumber(1000000)).toBe('10,00,000');
      expect(formatIndianNumber(10000000)).toBe('1,00,00,000');
    });
  });

  describe('validateDateRange', () => {
    it('should validate correct date range', () => {
      const startDate = new Date('2020-01-01');
      const endDate = new Date('2021-01-01');
      const result = validateDateRange(startDate, endDate);
      expect(result.isValid).toBe(true);
    });

    it('should reject start date after end date', () => {
      const startDate = new Date('2021-01-01');
      const endDate = new Date('2020-01-01');
      const result = validateDateRange(startDate, endDate);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Start date must be before end date');
    });

    it('should reject future dates', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);
      const startDate = new Date();
      const result = validateDateRange(startDate, futureDate);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('End date cannot be in the future');
    });
  });

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
    });
  });

  describe('safeJsonParse', () => {
    it('should parse valid JSON', () => {
      const jsonString = '{"test": "value"}';
      const result = safeJsonParse(jsonString, {});
      expect(result).toEqual({ test: 'value' });
    });

    it('should return default value for invalid JSON', () => {
      const invalidJson = '{"test": invalid}';
      const defaultValue = { default: true };
      const result = safeJsonParse(invalidJson, defaultValue);
      expect(result).toEqual(defaultValue);
    });
  });
});
