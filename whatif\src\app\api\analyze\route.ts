import { NextRequest, NextResponse } from 'next/server';
import { AngelOneClient } from '@/lib/api/angelone';
import { StockDataService } from '@/lib/services/stockData';
import { BenchmarkDataService } from '@/lib/services/benchmarkData';
import { InvestmentCalculator } from '@/lib/services/investmentCalculator';
import { ComparisonService } from '@/lib/services/comparisonService';
import { InvestmentScenario } from '@/lib/types';
import { generateId } from '@/lib/utils';

// Initialize services
let angelOneClient: AngelOneClient;
let stockDataService: StockDataService;
let benchmarkDataService: BenchmarkDataService;
let investmentCalculator: InvestmentCalculator;
let comparisonService: ComparisonService;

async function initializeServices() {
  if (!angelOneClient) {
    console.log('🔧 Creating Angel One client...');

    // Check environment variables
    const requiredEnvVars = {
      apiKey: process.env.ANGEL_ONE_API_KEY,
      clientId: process.env.ANGEL_ONE_CLIENT_ID,
      password: process.env.ANGEL_ONE_PASSWORD,
      totpSecret: process.env.ANGEL_ONE_TOTP_SECRET,
    };

    console.log('🔍 Environment variables check:', {
      apiKey: requiredEnvVars.apiKey ? 'SET' : 'MISSING',
      clientId: requiredEnvVars.clientId ? 'SET' : 'MISSING',
      password: requiredEnvVars.password ? 'SET' : 'MISSING',
      totpSecret: requiredEnvVars.totpSecret ? 'SET' : 'MISSING',
    });

    // Initialize Angel One client with environment variables
    angelOneClient = new AngelOneClient({
      apiKey: requiredEnvVars.apiKey!,
      clientId: requiredEnvVars.clientId!,
      password: requiredEnvVars.password!,
      totpSecret: requiredEnvVars.totpSecret!,
    });

    console.log('🔐 Attempting Angel One authentication...');

    // Authenticate
    const loginResult = await angelOneClient.login();
    console.log('🔐 Login result:', loginResult);

    if (!loginResult.success) {
      throw new Error(`Angel One authentication failed: ${loginResult.message}`);
    }

    console.log('✅ Angel One authentication successful');

    // Initialize services
    console.log('🔧 Initializing services...');
    stockDataService = new StockDataService(angelOneClient);
    benchmarkDataService = new BenchmarkDataService(angelOneClient);
    investmentCalculator = new InvestmentCalculator(stockDataService, benchmarkDataService);
    comparisonService = new ComparisonService(investmentCalculator, benchmarkDataService);

    console.log('✅ All services initialized successfully');
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting investment analysis...');

    // Initialize services if not already done
    console.log('🔧 Initializing services...');
    await initializeServices();
    console.log('✅ Services initialized successfully');

    // Parse request body
    console.log('📥 Parsing request body...');
    const body = await request.json();
    const { stockSymbol, investmentAmount, startDate, endDate } = body;

    console.log('📊 Request data:', {
      stockSymbol,
      investmentAmount,
      startDate,
      endDate
    });

    // Validate input
    if (!stockSymbol || !investmentAmount || !startDate || !endDate) {
      console.log('❌ Validation failed: Missing required fields');
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create investment scenario
    const scenario: InvestmentScenario = {
      id: generateId(),
      stockSymbol,
      investmentAmount: parseFloat(investmentAmount),
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      createdAt: new Date(),
    };

    // Validate date range
    if (scenario.startDate >= scenario.endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    if (scenario.startDate > new Date()) {
      return NextResponse.json(
        { error: 'Start date cannot be in the future' },
        { status: 400 }
      );
    }

    // Calculate investment result
    console.log('📊 Calculating investment result...');
    const investmentResult = await investmentCalculator.calculateInvestmentResult(scenario);
    console.log('✅ Investment result calculated');

    // Calculate comparison with benchmarks
    console.log('📊 Calculating benchmark comparisons...');
    const comparisonResult = await investmentCalculator.calculateWithComparisons(scenario);
    console.log('✅ Benchmark comparisons calculated');

    // Generate comparison summary
    console.log('📊 Generating comparison summary...');
    const comparisonSummary = await comparisonService.generateComparisonSummary(scenario);
    console.log('✅ Comparison summary generated');

    // Generate chart data
    console.log('📊 Generating chart data...');
    const chartData = await comparisonService.generateComparisonChartData(scenario);
    console.log('✅ Chart data generated');

    // Return results
    return NextResponse.json({
      investmentResult,
      comparisonResult,
      comparisonSummary,
      chartData,
    });

  } catch (error) {
    console.error('💥 Analysis API error:', error);
    console.error('Error type:', error?.constructor?.name);
    console.error('Error message:', error?.message);
    console.error('Error stack:', error?.stack);

    // Handle specific error types
    if (error instanceof Error) {
      console.log('🔍 Checking error type:', error.message);

      if (error.message.includes('Stock not found') || error.message.includes('Invalid stock symbol')) {
        console.log('❌ Stock not found error');
        return NextResponse.json(
          { error: 'Stock symbol not found. Please check the symbol and try again.' },
          { status: 404 }
        );
      }

      if (error.message.includes('Rate limit')) {
        console.log('❌ Rate limit error');
        return NextResponse.json(
          { error: 'Too many requests. Please wait a moment and try again.' },
          { status: 429 }
        );
      }

      if (error.message.includes('Authentication')) {
        console.log('❌ Authentication error');
        return NextResponse.json(
          { error: 'Authentication failed. Please try again later.' },
          { status: 401 }
        );
      }
    }

    console.log('❌ Generic error, returning 500');
    return NextResponse.json(
      { error: 'An error occurred while analyzing the investment. Please try again.', details: error?.message },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    await initializeServices();
    return NextResponse.json({ status: 'OK', message: 'Analysis API is running' });
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json(
      { status: 'ERROR', message: 'Service unavailable' },
      { status: 503 }
    );
  }
}
