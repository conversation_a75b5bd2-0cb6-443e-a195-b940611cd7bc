import { StockDataService } from '../stockData';
import { InvestmentScenario } from '../../types';
import {
  getRealStockDataService,
  TEST_STOCKS,
  TEST_DATE_RANGES,
  TEST_AMOUNTS,
  validateRealData,
  waitForRateLimit,
  retryApiCall,
  cleanupTestSession
} from '../../test-config/realApiSetup';

// REAL API TESTS - NO MOCKS
describe('StockDataService - Real API Integration', () => {
  let stockDataService: StockDataService;

  beforeAll(async () => {
    stockDataService = await getRealStockDataService();
  });

  afterAll(async () => {
    await cleanupTestSession();
  });

  describe('getHistoricalPrices - Real API', () => {
    it('should fetch real historical prices for SBIN', async () => {
      await waitForRateLimit();

      const result = await retryApiCall(() =>
        stockDataService.getHistoricalPrices(
          TEST_STOCKS.SBIN.token,
          TEST_STOCKS.SBIN.exchange,
          TEST_DATE_RANGES.SHORT_TERM.start,
          TEST_DATE_RANGES.SHORT_TERM.end
        )
      );

      validateRealData(result, 'Historical prices');
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('date');
      expect(result[0]).toHaveProperty('open');
      expect(result[0]).toHaveProperty('high');
      expect(result[0]).toHaveProperty('low');
      expect(result[0]).toHaveProperty('close');
      expect(result[0]).toHaveProperty('volume');

      // Validate data is realistic
      expect(result[0].open).toBeGreaterThan(0);
      expect(result[0].high).toBeGreaterThanOrEqual(result[0].open);
      expect(result[0].low).toBeLessThanOrEqual(result[0].open);
      expect(result[0].volume).toBeGreaterThan(0);

      console.log(`✅ Fetched ${result.length} real historical data points for SBIN`);
    }, 30000);

    it('should validate date range with real API', async () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);

      await expect(
        stockDataService.getHistoricalPrices(
          TEST_STOCKS.SBIN.token,
          TEST_STOCKS.SBIN.exchange,
          new Date(),
          futureDate
        )
      ).rejects.toThrow('End date cannot be in the future');
    });

    it('should handle invalid stock token with real API', async () => {
      await waitForRateLimit();

      await expect(
        stockDataService.getHistoricalPrices(
          '99999', // Invalid token
          'NSE',
          TEST_DATE_RANGES.SHORT_TERM.start,
          TEST_DATE_RANGES.SHORT_TERM.end
        )
      ).rejects.toThrow();
    }, 15000);
  });

  describe('getCurrentPrice - Real API', () => {
    it('should fetch real current price for SBIN', async () => {
      await waitForRateLimit();

      const result = await retryApiCall(() =>
        stockDataService.getCurrentPrice(
          TEST_STOCKS.SBIN.symbol,
          TEST_STOCKS.SBIN.token,
          TEST_STOCKS.SBIN.exchange
        )
      );

      validateRealData(result, 'Current price data');
      expect(result.symbol).toBe(TEST_STOCKS.SBIN.symbol);
      expect(result.exchange).toBe(TEST_STOCKS.SBIN.exchange);
      expect(result.token).toBe(TEST_STOCKS.SBIN.token);
      expect(result.currentPrice).toBeGreaterThan(0);
      expect(result.lastUpdated).toBeInstanceOf(Date);

      console.log(`✅ Real current price for SBIN: ₹${result.currentPrice}`);
    }, 15000);

    it('should fetch real current price for multiple stocks', async () => {
      const stocks = [TEST_STOCKS.SBIN, TEST_STOCKS.RELIANCE];

      for (const stock of stocks) {
        await waitForRateLimit();

        const result = await retryApiCall(() =>
          stockDataService.getCurrentPrice(stock.symbol, stock.token, stock.exchange)
        );

        validateRealData(result, `Current price for ${stock.symbol}`);
        expect(result.currentPrice).toBeGreaterThan(0);
        console.log(`✅ Real current price for ${stock.symbol}: ₹${result.currentPrice}`);
      }
    }, 30000);

    it('should handle invalid stock symbol with real API', async () => {
      await waitForRateLimit();

      await expect(
        stockDataService.getCurrentPrice('INVALID-EQ', '99999', 'NSE')
      ).rejects.toThrow();
    }, 15000);
  });

  describe('calculateInvestmentResult - Real API', () => {
    it('should calculate real investment result for SBIN', async () => {
      await waitForRateLimit();

      const scenario: InvestmentScenario = {
        id: 'real-test-scenario',
        stockSymbol: TEST_STOCKS.SBIN.token,
        investmentAmount: TEST_AMOUNTS.MEDIUM,
        startDate: TEST_DATE_RANGES.SHORT_TERM.start,
        endDate: TEST_DATE_RANGES.SHORT_TERM.end,
        createdAt: new Date(),
      };

      const result = await retryApiCall(() =>
        stockDataService.calculateInvestmentResult(scenario)
      );

      validateRealData(result, 'Investment result');
      expect(result.scenario).toEqual(scenario);
      expect(result.initialValue).toBe(TEST_AMOUNTS.MEDIUM);
      expect(result.currentValue).toBeGreaterThan(0);
      expect(typeof result.absoluteReturn).toBe('number');
      expect(typeof result.cagr).toBe('number');
      expect(typeof result.totalReturn).toBe('number');

      console.log(`✅ Real investment result for SBIN:`);
      console.log(`   Initial: ₹${result.initialValue.toLocaleString()}`);
      console.log(`   Current: ₹${result.currentValue.toLocaleString()}`);
      console.log(`   Return: ${result.absoluteReturn}%`);
      console.log(`   CAGR: ${result.cagr}%`);
    }, 30000);

    it('should calculate investment results for different amounts', async () => {
      const amounts = [TEST_AMOUNTS.SMALL, TEST_AMOUNTS.MEDIUM];

      for (const amount of amounts) {
        await waitForRateLimit();

        const scenario: InvestmentScenario = {
          id: `real-test-${amount}`,
          stockSymbol: TEST_STOCKS.SBIN.token,
          investmentAmount: amount,
          startDate: TEST_DATE_RANGES.SHORT_TERM.start,
          endDate: TEST_DATE_RANGES.SHORT_TERM.end,
          createdAt: new Date(),
        };

        const result = await retryApiCall(() =>
          stockDataService.calculateInvestmentResult(scenario)
        );

        validateRealData(result, `Investment result for ₹${amount}`);
        expect(result.initialValue).toBe(amount);
        console.log(`✅ Investment result for ₹${amount}: ${result.absoluteReturn}% return`);
      }
    }, 45000);
  });

  describe('getPriceAtDate - Real API', () => {
    it('should find real price at specific date for SBIN', async () => {
      await waitForRateLimit();

      const targetDate = new Date('2024-01-15'); // Mid-month date

      const result = await retryApiCall(() =>
        stockDataService.getPriceAtDate(
          TEST_STOCKS.SBIN.token,
          TEST_STOCKS.SBIN.exchange,
          targetDate
        )
      );

      if (result) {
        validateRealData(result, 'Price at date');
        expect(result.price).toBeGreaterThan(0);
        expect(result.actualDate).toBeInstanceOf(Date);

        console.log(`✅ Real price for SBIN on ${targetDate.toDateString()}: ₹${result.price} (actual date: ${result.actualDate.toDateString()})`);
      } else {
        console.log('⚠️ No price data available for the specified date (market holiday or weekend)');
      }
    }, 20000);

    it('should handle weekend/holiday dates gracefully', async () => {
      await waitForRateLimit();

      const weekendDate = new Date('2024-01-07'); // Sunday

      const result = await retryApiCall(() =>
        stockDataService.getPriceAtDate(
          TEST_STOCKS.SBIN.token,
          TEST_STOCKS.SBIN.exchange,
          weekendDate
        )
      );

      // Should either return null or return closest trading day
      if (result) {
        expect(result.price).toBeGreaterThan(0);
        console.log(`✅ Found closest trading day price: ₹${result.price} on ${result.actualDate.toDateString()}`);
      } else {
        console.log('✅ Correctly returned null for non-trading day');
      }
    }, 20000);
  });

  describe('validateStock - Real API', () => {
    it('should validate real SBIN stock', async () => {
      await waitForRateLimit();

      const result = await retryApiCall(() =>
        stockDataService.validateStock(
          TEST_STOCKS.SBIN.symbol,
          TEST_STOCKS.SBIN.token,
          TEST_STOCKS.SBIN.exchange
        )
      );

      expect(result.isValid).toBe(true);
      expect(result.stockData).toBeDefined();
      expect(result.error).toBeUndefined();

      if (result.stockData) {
        validateRealData(result.stockData, 'Stock validation data');
        expect(result.stockData.symbol).toBe(TEST_STOCKS.SBIN.symbol);
        expect(result.stockData.currentPrice).toBeGreaterThan(0);
        console.log(`✅ SBIN validation successful: ₹${result.stockData.currentPrice}`);
      }
    }, 15000);

    it('should handle invalid stock with real API', async () => {
      await waitForRateLimit();

      const result = await stockDataService.validateStock('INVALID-EQ', '99999', 'NSE');

      expect(result.isValid).toBe(false);
      expect(result.stockData).toBeUndefined();
      expect(result.error).toBeDefined();
      console.log(`✅ Invalid stock correctly rejected: ${result.error}`);
    }, 15000);

    it('should validate multiple real stocks', async () => {
      const stocks = [TEST_STOCKS.SBIN, TEST_STOCKS.RELIANCE];

      for (const stock of stocks) {
        await waitForRateLimit();

        const result = await retryApiCall(() =>
          stockDataService.validateStock(stock.symbol, stock.token, stock.exchange)
        );

        expect(result.isValid).toBe(true);
        expect(result.stockData).toBeDefined();

        if (result.stockData) {
          validateRealData(result.stockData, `${stock.symbol} validation`);
          console.log(`✅ ${stock.symbol} validation: ₹${result.stockData.currentPrice}`);
        }
      }
    }, 30000);
  });
});
