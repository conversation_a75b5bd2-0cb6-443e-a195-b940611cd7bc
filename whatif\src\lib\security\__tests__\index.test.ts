import {
  validateEnvironment,
  sanitizeForLogging,
  simpleEncrypt,
  simpleDecrypt,
  validateApi<PERSON>ey,
  RateLimiter,
} from '../index';

describe('Security Utilities', () => {
  describe('validateEnvironment', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      jest.resetModules();
      process.env = { ...originalEnv };
    });

    afterAll(() => {
      process.env = originalEnv;
    });

    it('should validate required environment variables', () => {
      process.env.NEXT_PUBLIC_ANGEL_ONE_API_URL = 'https://api.example.com';
      process.env.NEXT_PUBLIC_APP_NAME = 'Test App';
      process.env.NEXT_PUBLIC_APP_VERSION = '1.0.0';

      const result = validateEnvironment();
      expect(result.isValid).toBe(true);
      expect(result.missingVars).toHaveLength(0);
    });

    it('should detect missing required variables', () => {
      delete process.env.NEXT_PUBLIC_ANGEL_ONE_API_URL;

      const result = validateEnvironment();
      expect(result.isValid).toBe(false);
      expect(result.missingVars).toContain('NEXT_PUBLIC_ANGEL_ONE_API_URL');
    });

    it('should warn about missing optional variables', () => {
      process.env.NEXT_PUBLIC_ANGEL_ONE_API_URL = 'https://api.example.com';
      process.env.NEXT_PUBLIC_APP_NAME = 'Test App';
      process.env.NEXT_PUBLIC_APP_VERSION = '1.0.0';

      const result = validateEnvironment();
      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings[0]).toContain('API functionality will be limited');
    });
  });

  describe('sanitizeForLogging', () => {
    it('should redact sensitive keys', () => {
      const data = {
        username: 'testuser',
        password: 'secret123',
        apiKey: 'abc123',
        normalField: 'visible',
      };

      const sanitized = sanitizeForLogging(data);
      expect(sanitized.username).toBe('testuser');
      expect(sanitized.password).toBe('[REDACTED]');
      expect(sanitized.apiKey).toBe('[REDACTED]');
      expect(sanitized.normalField).toBe('visible');
    });

    it('should handle nested objects', () => {
      const data = {
        user: {
          name: 'test',
          token: 'secret',
        },
        config: {
          apiKey: 'secret',
          timeout: 5000,
        },
      };

      const sanitized = sanitizeForLogging(data);
      expect((sanitized.user as any).name).toBe('test');
      expect((sanitized.user as any).token).toBe('[REDACTED]');
      expect((sanitized.config as any).apiKey).toBe('[REDACTED]');
      expect((sanitized.config as any).timeout).toBe(5000);
    });
  });

  describe('simpleEncrypt and simpleDecrypt', () => {
    it('should encrypt and decrypt text correctly', () => {
      const originalText = 'Hello World!';
      const encrypted = simpleEncrypt(originalText);
      const decrypted = simpleDecrypt(encrypted);

      expect(encrypted).not.toBe(originalText);
      expect(decrypted).toBe(originalText);
    });

    it('should handle empty strings', () => {
      const encrypted = simpleEncrypt('');
      const decrypted = simpleDecrypt(encrypted);
      expect(decrypted).toBe('');
    });
  });

  describe('validateApiKey', () => {
    it('should validate correct API key', () => {
      const result = validateApiKey('valid_api_key_123');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject empty API key', () => {
      const result = validateApiKey('');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('API key is required');
    });

    it('should reject short API key', () => {
      const result = validateApiKey('short');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('API key is too short');
    });

    it('should reject API key with spaces', () => {
      const result = validateApiKey('api key with spaces');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('API key should not contain spaces');
    });
  });

  describe('RateLimiter', () => {
    it('should allow requests within limit', () => {
      const limiter = new RateLimiter(5, 60); // 5 requests per minute

      for (let i = 0; i < 5; i++) {
        expect(limiter.isAllowed()).toBe(true);
      }
    });

    it('should reject requests over limit', () => {
      const limiter = new RateLimiter(2, 60); // 2 requests per minute

      expect(limiter.isAllowed()).toBe(true);
      expect(limiter.isAllowed()).toBe(true);
      expect(limiter.isAllowed()).toBe(false);
    });

    it('should reset after time window', async () => {
      const limiter = new RateLimiter(1, 1); // 1 request per second

      expect(limiter.isAllowed()).toBe(true);
      expect(limiter.isAllowed()).toBe(false);

      // Wait for reset
      await new Promise(resolve => setTimeout(resolve, 1100));
      expect(limiter.isAllowed()).toBe(true);
    });

    it('should calculate time until reset correctly', () => {
      const limiter = new RateLimiter(1, 60); // 1 request per minute

      limiter.isAllowed(); // Use up the limit
      const timeUntilReset = limiter.getTimeUntilReset();
      
      expect(timeUntilReset).toBeGreaterThan(0);
      expect(timeUntilReset).toBeLessThanOrEqual(60000); // Should be within 60 seconds
    });
  });
});
