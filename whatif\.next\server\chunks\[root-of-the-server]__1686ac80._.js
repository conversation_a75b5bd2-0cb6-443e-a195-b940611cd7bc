module.exports = {

"[project]/.next-internal/server/app/api/analyze-mock/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/analyze-mock/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request) {
    try {
        console.log('🧪 Mock analysis API called');
        // Parse request body
        const body = await request.json();
        const { stockSymbol, investmentAmount, startDate, endDate } = body;
        console.log('📊 Mock analysis request:', {
            stockSymbol,
            investmentAmount,
            startDate,
            endDate
        });
        // Validate input
        if (!stockSymbol || !investmentAmount || !startDate || !endDate) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields'
            }, {
                status: 400
            });
        }
        // Mock calculation - simulate TCS investment growth
        const start = new Date(startDate);
        const end = new Date(endDate);
        const years = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
        // Mock TCS performance (approximate historical performance)
        const mockCAGR = 15.5; // TCS has historically performed well
        const currentValue = investmentAmount * Math.pow(1 + mockCAGR / 100, years);
        const absoluteReturn = (currentValue - investmentAmount) / investmentAmount * 100;
        // Mock investment result
        const investmentResult = {
            scenario: {
                id: `mock-${Date.now()}`,
                stockSymbol,
                investmentAmount: parseFloat(investmentAmount),
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                createdAt: new Date()
            },
            initialValue: investmentAmount,
            currentValue: Math.round(currentValue),
            absoluteReturn: Math.round(absoluteReturn * 100) / 100,
            cagr: mockCAGR,
            totalReturn: Math.round((currentValue - investmentAmount) * 100) / 100,
            annualizedReturn: mockCAGR
        };
        // Mock benchmark data
        const comparisonResult = {
            investment: {
                name: `${stockSymbol} Investment`,
                initialValue: investmentAmount,
                currentValue: Math.round(currentValue),
                cagr: mockCAGR,
                absoluteReturn: Math.round(absoluteReturn * 100) / 100
            },
            benchmarks: [
                {
                    name: 'Gold',
                    initialValue: investmentAmount,
                    currentValue: Math.round(investmentAmount * Math.pow(1.08, years)),
                    cagr: 8.0,
                    absoluteReturn: Math.round((investmentAmount * Math.pow(1.08, years) - investmentAmount) / investmentAmount * 10000) / 100
                },
                {
                    name: 'Fixed Deposit',
                    initialValue: investmentAmount,
                    currentValue: Math.round(investmentAmount * Math.pow(1.065, years)),
                    cagr: 6.5,
                    absoluteReturn: Math.round((investmentAmount * Math.pow(1.065, years) - investmentAmount) / investmentAmount * 10000) / 100
                },
                {
                    name: 'Nifty 50',
                    initialValue: investmentAmount,
                    currentValue: Math.round(investmentAmount * Math.pow(1.12, years)),
                    cagr: 12.0,
                    absoluteReturn: Math.round((investmentAmount * Math.pow(1.12, years) - investmentAmount) / investmentAmount * 10000) / 100
                }
            ]
        };
        // Mock comparison summary
        const comparisonSummary = {
            investment: {
                name: `${stockSymbol} Investment`,
                cagr: mockCAGR,
                totalReturn: Math.round((currentValue - investmentAmount) * 100) / 100
            },
            benchmarks: comparisonResult.benchmarks,
            insights: [
                `Your ${stockSymbol} investment outperformed Fixed Deposits by ${(mockCAGR - 6.5).toFixed(1)}% annually`,
                `${stockSymbol} delivered ${mockCAGR}% CAGR over the ${years.toFixed(1)} year period`,
                `Total wealth created: ₹${Math.round(currentValue - investmentAmount).toLocaleString('en-IN')}`,
                `Investment multiplied by ${(currentValue / investmentAmount).toFixed(2)}x`
            ]
        };
        // Mock chart data
        const chartData = {
            timeSeriesData: [
                {
                    date: startDate,
                    value: investmentAmount,
                    label: 'Investment Start'
                },
                {
                    date: endDate,
                    value: Math.round(currentValue),
                    label: 'Current Value'
                }
            ],
            barChartData: [
                {
                    name: stockSymbol,
                    value: mockCAGR,
                    color: '#3b82f6'
                },
                {
                    name: 'Nifty 50',
                    value: 12.0,
                    color: '#8b5cf6'
                },
                {
                    name: 'Gold',
                    value: 8.0,
                    color: '#f59e0b'
                },
                {
                    name: 'FD',
                    value: 6.5,
                    color: '#10b981'
                }
            ]
        };
        console.log('✅ Mock analysis completed successfully');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            investmentResult,
            comparisonResult,
            comparisonSummary,
            chartData
        });
    } catch (error) {
        console.error('❌ Mock analysis error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Mock analysis failed',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        status: 'OK',
        message: 'Mock Analysis API is running',
        note: 'This is a mock implementation for testing purposes'
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1686ac80._.js.map