module.exports = {

"[project]/.next-internal/server/app/api/analyze/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/config/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Configuration for the What If investment analysis tool
__turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG),
    "APP_CONFIG": (()=>APP_CONFIG),
    "BENCHMARK_CONFIG": (()=>BENCHMARK_CONFIG),
    "CALCULATION_CONFIG": (()=>CALCULATION_CONFIG),
    "MARKET_CONFIG": (()=>MARKET_CONFIG),
    "STORAGE_CONFIG": (()=>STORAGE_CONFIG),
    "UI_CONFIG": (()=>UI_CONFIG),
    "getEnvironmentConfig": (()=>getEnvironmentConfig)
});
const APP_CONFIG = {
    name: 'What If',
    description: 'Investment Analysis Tool for Indian Equities',
    version: '1.0.0',
    author: 'What If Team'
};
const API_CONFIG = {
    angelOne: {
        baseUrl: 'https://apiconnect.angelone.in',
        version: 'v1',
        endpoints: {
            login: '/rest/auth/angelbroking/user/v1/loginByPassword',
            profile: '/rest/secure/angelbroking/user/v1/getProfile',
            historicalData: '/rest/secure/angelbroking/historical/v1/getCandleData',
            ltp: '/rest/secure/angelbroking/order/v1/getLtpData',
            logout: '/rest/secure/angelbroking/user/v1/logout'
        }
    },
    rateLimit: {
        requestsPerSecond: 10,
        requestsPerMinute: 100
    }
};
const MARKET_CONFIG = {
    exchanges: [
        'NSE',
        'BSE'
    ],
    segments: [
        'EQUITY'
    ],
    tradingHours: {
        start: '09:15',
        end: '15:30',
        timezone: 'Asia/Kolkata'
    },
    holidays: []
};
const BENCHMARK_CONFIG = {
    types: {
        GOLD: {
            name: 'Gold',
            symbol: 'GOLD',
            description: 'Gold prices in INR per 10 grams'
        },
        FD: {
            name: 'Fixed Deposit',
            symbol: 'FD',
            description: 'Average FD rates from major banks',
            defaultRate: 6.5
        },
        NIFTY: {
            name: 'Nifty 50',
            symbol: 'NIFTY',
            description: 'NSE Nifty 50 Index',
            token: '********'
        }
    }
};
const CALCULATION_CONFIG = {
    precision: {
        currency: 2,
        percentage: 2,
        cagr: 2
    },
    defaults: {
        fdRate: 6.5,
        inflationRate: 6.0
    }
};
const UI_CONFIG = {
    theme: {
        primary: '#1f2937',
        secondary: '#374151',
        accent: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
    },
    charts: {
        defaultHeight: 400,
        colors: {
            investment: '#3b82f6',
            gold: '#f59e0b',
            fd: '#10b981',
            nifty: '#8b5cf6'
        }
    }
};
const STORAGE_CONFIG = {
    keys: {
        userPreferences: 'whatif_user_preferences',
        savedScenarios: 'whatif_saved_scenarios',
        apiCredentials: 'whatif_api_credentials'
    },
    encryption: {
        enabled: true,
        algorithm: 'AES-256-GCM'
    }
};
const getEnvironmentConfig = ()=>{
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
    return {
        isDevelopment,
        isProduction,
        apiUrl: process.env.NEXT_PUBLIC_API_URL || API_CONFIG.angelOne.baseUrl,
        enableLogging: isDevelopment,
        enableAnalytics: isProduction
    };
};
}}),
"[project]/src/lib/security/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Security utilities for the What If investment analysis tool
__turbopack_context__.s({
    "RateLimiter": (()=>RateLimiter),
    "sanitizeForLogging": (()=>sanitizeForLogging),
    "simpleDecrypt": (()=>simpleDecrypt),
    "simpleEncrypt": (()=>simpleEncrypt),
    "validateApiKey": (()=>validateApiKey),
    "validateEnvironment": (()=>validateEnvironment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/index.ts [app-route] (ecmascript)");
;
function validateEnvironment() {
    const requiredVars = [
        'NEXT_PUBLIC_ANGEL_ONE_API_URL',
        'NEXT_PUBLIC_APP_NAME',
        'NEXT_PUBLIC_APP_VERSION'
    ];
    const optionalVars = [
        'ANGEL_ONE_API_KEY',
        'ANGEL_ONE_CLIENT_ID',
        'ANGEL_ONE_PASSWORD',
        'ANGEL_ONE_TOTP_SECRET'
    ];
    const missingVars = [];
    const warnings = [];
    // Check required variables
    requiredVars.forEach((varName)=>{
        if (!process.env[varName]) {
            missingVars.push(varName);
        }
    });
    // Check optional but important variables
    optionalVars.forEach((varName)=>{
        if (!process.env[varName]) {
            warnings.push(`${varName} is not set - API functionality will be limited`);
        }
    });
    // Check for development secrets in production
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return {
        isValid: missingVars.length === 0,
        missingVars,
        warnings
    };
}
function sanitizeForLogging(data, visited = new WeakSet()) {
    const sensitiveKeys = [
        'password',
        'token',
        'secret',
        'key',
        'auth',
        'credential',
        'totp'
    ];
    // Handle non-object types
    if (typeof data !== 'object' || data === null) {
        return {
            value: data
        };
    }
    // Handle circular references
    if (visited.has(data)) {
        return {
            '[Circular Reference]': true
        };
    }
    visited.add(data);
    const sanitized = {};
    try {
        Object.keys(data).forEach((key)=>{
            const lowerKey = key.toLowerCase();
            const isSensitive = sensitiveKeys.some((sensitiveKey)=>lowerKey.includes(sensitiveKey));
            if (isSensitive) {
                sanitized[key] = '[REDACTED]';
            } else if (typeof data[key] === 'object' && data[key] !== null) {
                // Handle arrays
                if (Array.isArray(data[key])) {
                    sanitized[key] = '[Array]';
                } else {
                    sanitized[key] = sanitizeForLogging(data[key], visited);
                }
            } else {
                sanitized[key] = data[key];
            }
        });
    } catch (error) {
        return {
            '[Sanitization Error]': 'Unable to sanitize object'
        };
    }
    return sanitized;
}
function simpleEncrypt(text) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["STORAGE_CONFIG"].encryption.enabled) {
        return text;
    }
    // Simple base64 encoding with character shifting (not secure, just obfuscation)
    const shifted = text.split('').map((char)=>String.fromCharCode(char.charCodeAt(0) + 3)).join('');
    return btoa(shifted);
}
function simpleDecrypt(encryptedText) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["STORAGE_CONFIG"].encryption.enabled) {
        return encryptedText;
    }
    try {
        const decoded = atob(encryptedText);
        return decoded.split('').map((char)=>String.fromCharCode(char.charCodeAt(0) - 3)).join('');
    } catch  {
        return encryptedText; // Return as-is if decryption fails
    }
}
function validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
        return {
            isValid: false,
            error: 'API key is required'
        };
    }
    if (apiKey.length < 10) {
        return {
            isValid: false,
            error: 'API key is too short'
        };
    }
    if (apiKey.includes(' ')) {
        return {
            isValid: false,
            error: 'API key should not contain spaces'
        };
    }
    return {
        isValid: true
    };
}
class RateLimiter {
    requests = [];
    maxRequests;
    timeWindow;
    constructor(maxRequests, timeWindowSeconds){
        this.maxRequests = maxRequests;
        this.timeWindow = timeWindowSeconds * 1000;
    }
    /**
   * Check if request is allowed
   * @returns Whether request is allowed
   */ isAllowed() {
        const now = Date.now();
        // Remove old requests outside the time window
        this.requests = this.requests.filter((time)=>now - time < this.timeWindow);
        // Check if we're under the limit
        if (this.requests.length < this.maxRequests) {
            this.requests.push(now);
            return true;
        }
        return false;
    }
    /**
   * Get time until next request is allowed
   * @returns Milliseconds until next request
   */ getTimeUntilReset() {
        if (this.requests.length === 0) return 0;
        const oldestRequest = Math.min(...this.requests);
        const timeUntilReset = this.timeWindow - (Date.now() - oldestRequest);
        return Math.max(0, timeUntilReset);
    }
}
}}),
"[project]/src/lib/api/angelone.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Angel One API client for the What If investment analysis tool
__turbopack_context__.s({
    "AngelOneClient": (()=>AngelOneClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$otplib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/otplib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-route] (ecmascript)");
;
;
;
;
class AngelOneClient {
    axiosInstance;
    jwtToken = null;
    refreshToken = null;
    feedToken = null;
    rateLimiter;
    apiKey;
    clientId;
    password;
    totpSecret;
    constructor(config){
        this.apiKey = config.apiKey;
        this.clientId = config.clientId;
        this.password = config.password;
        this.totpSecret = config.totpSecret;
        // Initialize rate limiter
        this.rateLimiter = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RateLimiter"](__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].rateLimit.requestsPerSecond, 1);
        // Create axios instance
        this.axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-UserType': 'USER',
                'X-SourceID': 'WEB',
                'X-ClientLocalIP': '127.0.0.1',
                'X-ClientPublicIP': '127.0.0.1',
                'X-MACAddress': '00:00:00:00:00:00',
                'X-PrivateKey': this.apiKey
            }
        });
        // Add request interceptor for rate limiting
        this.axiosInstance.interceptors.request.use(async (config)=>{
            // Wait if rate limit exceeded
            while(!this.rateLimiter.isAllowed()){
                const waitTime = this.rateLimiter.getTimeUntilReset();
                await new Promise((resolve)=>setTimeout(resolve, waitTime));
            }
            // Add JWT token if available
            if (this.jwtToken) {
                config.headers.Authorization = `Bearer ${this.jwtToken}`;
            }
            return config;
        }, (error)=>Promise.reject(error));
        // Add response interceptor for error handling
        this.axiosInstance.interceptors.response.use((response)=>response, async (error)=>{
            if (error.response?.status === 401 && this.refreshToken) {
                // Try to refresh token
                try {
                    await this.refreshAuthToken();
                    // Retry the original request
                    return this.axiosInstance.request(error.config);
                } catch  {
                    // Refresh failed, need to re-login
                    this.clearTokens();
                    throw new Error('Authentication failed. Please login again.');
                }
            }
            return Promise.reject(error);
        });
    }
    /**
   * Generate TOTP for authentication
   */ generateTOTP() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$otplib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authenticator"].generate(this.totpSecret);
    }
    /**
   * Clear stored tokens
   */ clearTokens() {
        this.jwtToken = null;
        this.refreshToken = null;
        this.feedToken = null;
    }
    /**
   * Login to Angel One API
   */ async login() {
        try {
            const totp = this.generateTOTP();
            const loginRequest = {
                clientcode: this.clientId,
                password: this.password,
                totp: totp
            };
            console.log('Attempting login with:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])(loginRequest));
            const response = await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.login, loginRequest);
            if (response.data.status && response.data.data) {
                this.jwtToken = response.data.data.jwtToken;
                this.refreshToken = response.data.data.refreshToken;
                this.feedToken = response.data.data.feedToken;
                return {
                    success: true,
                    message: 'Login successful'
                };
            } else {
                return {
                    success: false,
                    message: response.data.message || 'Login failed'
                };
            }
        } catch (error) {
            console.error('Login error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error
            }));
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Login failed'
            };
        }
    }
    /**
   * Refresh authentication token
   */ async refreshAuthToken() {
        if (!this.refreshToken) {
            throw new Error('No refresh token available');
        }
        const response = await this.axiosInstance.post('/rest/auth/angelbroking/jwt/v1/generateTokens', {
            refreshToken: this.refreshToken
        });
        if (response.data.status && response.data.data) {
            this.jwtToken = response.data.data.jwtToken;
            this.refreshToken = response.data.data.refreshToken;
        } else {
            throw new Error('Token refresh failed');
        }
    }
    /**
   * Check if client is authenticated
   */ isAuthenticated() {
        return this.jwtToken !== null;
    }
    /**
   * Get historical data for a stock (optimized for closing prices only)
   */ async getHistoricalData(request) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated. Please login first.');
        }
        try {
            const response = await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.historicalData, request);
            if (response.data.status && response.data.data) {
                // Optimized: Only extract date and closing price for investment calculations
                return response.data.data.map((item)=>({
                        date: new Date(item[0]),
                        close: item[4]
                    }));
            } else {
                throw new Error(response.data.message || 'Failed to fetch historical data');
            }
        } catch (error) {
            console.error('Historical data error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error,
                request
            }));
            throw error;
        }
    }
    /**
   * Get full OHLC historical data (if needed for advanced analysis)
   */ async getFullHistoricalData(request) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated. Please login first.');
        }
        try {
            const response = await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.historicalData, request);
            if (response.data.status && response.data.data) {
                return response.data.data.map((item)=>({
                        date: new Date(item[0]),
                        open: item[1],
                        high: item[2],
                        low: item[3],
                        close: item[4],
                        volume: item[5]
                    }));
            } else {
                throw new Error(response.data.message || 'Failed to fetch historical data');
            }
        } catch (error) {
            console.error('Full historical data error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error,
                request
            }));
            throw error;
        }
    }
    /**
   * Get current price (LTP) for a stock
   */ async getCurrentPrice(request) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated. Please login first.');
        }
        try {
            const response = await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.ltp, request);
            if (response.data.status && response.data.data) {
                const data = response.data.data;
                return {
                    symbol: data.tradingsymbol,
                    name: data.tradingsymbol,
                    exchange: data.exchange,
                    token: data.symboltoken,
                    currentPrice: data.ltp,
                    lastUpdated: new Date()
                };
            } else {
                throw new Error(response.data.message || 'Failed to fetch current price');
            }
        } catch (error) {
            console.error('Current price error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error,
                request
            }));
            throw error;
        }
    }
    /**
   * Logout from Angel One API
   */ async logout() {
        if (!this.isAuthenticated()) {
            return {
                success: true,
                message: 'Already logged out'
            };
        }
        try {
            await this.axiosInstance.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_CONFIG"].angelOne.endpoints.logout, {
                clientcode: this.clientId
            });
            this.clearTokens();
            return {
                success: true,
                message: 'Logout successful'
            };
        } catch (error) {
            console.error('Logout error:', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeForLogging"])({
                error
            }));
            this.clearTokens(); // Clear tokens anyway
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Logout failed'
            };
        }
    }
}
}}),
"[project]/src/lib/utils/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Utility functions for the What If investment analysis tool
__turbopack_context__.s({
    "calculateAbsoluteReturn": (()=>calculateAbsoluteReturn),
    "calculateCAGR": (()=>calculateCAGR),
    "calculateYearsBetweenDates": (()=>calculateYearsBetweenDates),
    "debounce": (()=>debounce),
    "formatCurrency": (()=>formatCurrency),
    "formatIndianNumber": (()=>formatIndianNumber),
    "formatPercentage": (()=>formatPercentage),
    "generateId": (()=>generateId),
    "isMarketOpen": (()=>isMarketOpen),
    "safeJsonParse": (()=>safeJsonParse),
    "validateDateRange": (()=>validateDateRange)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/index.ts [app-route] (ecmascript)");
;
function calculateCAGR(initialValue, finalValue, years) {
    if (initialValue <= 0 || finalValue <= 0 || years <= 0) {
        return 0;
    }
    const cagr = (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100;
    return Number(cagr.toFixed(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CALCULATION_CONFIG"].precision.cagr));
}
function calculateAbsoluteReturn(initialValue, finalValue) {
    if (initialValue <= 0) return 0;
    const absoluteReturn = (finalValue - initialValue) / initialValue * 100;
    return Number(absoluteReturn.toFixed(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CALCULATION_CONFIG"].precision.percentage));
}
function calculateYearsBetweenDates(startDate, endDate) {
    const timeDiff = endDate.getTime() - startDate.getTime();
    const daysDiff = timeDiff / (1000 * 3600 * 24);
    return daysDiff / 365.25; // Account for leap years
}
function formatCurrency(value, currency = 'INR') {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CALCULATION_CONFIG"].precision.currency,
        maximumFractionDigits: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CALCULATION_CONFIG"].precision.currency
    }).format(value);
}
function formatPercentage(value) {
    return `${value.toFixed(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CALCULATION_CONFIG"].precision.percentage)}%`;
}
function formatIndianNumber(value) {
    return new Intl.NumberFormat('en-IN').format(value);
}
function validateDateRange(startDate, endDate) {
    const now = new Date();
    if (startDate >= endDate) {
        return {
            isValid: false,
            error: 'Start date must be before end date'
        };
    }
    if (startDate > now) {
        return {
            isValid: false,
            error: 'Start date cannot be in the future'
        };
    }
    if (endDate > now) {
        return {
            isValid: false,
            error: 'End date cannot be in the future'
        };
    }
    // Angel One API historical data limits (based on testing)
    const angelOneMinDate = new Date('2018-01-01'); // Conservative estimate - Angel One reliable data from 2018
    if (startDate < angelOneMinDate) {
        return {
            isValid: false,
            error: `Start date cannot be earlier than ${angelOneMinDate.toLocaleDateString()}. Angel One API historical data is limited to recent years.`
        };
    }
    // Maximum date range validation (Angel One may have limits on large ranges)
    const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const maxDays = 3650; // ~10 years maximum
    if (daysDifference > maxDays) {
        return {
            isValid: false,
            error: `Date range too large (${daysDifference} days). Maximum allowed is ${maxDays} days (~10 years).`
        };
    }
    // Minimum date range validation
    const minDays = 30; // At least 1 month for meaningful analysis
    if (daysDifference < minDays) {
        return {
            isValid: false,
            error: `Date range too small (${daysDifference} days). Minimum required is ${minDays} days for meaningful analysis.`
        };
    }
    return {
        isValid: true
    };
}
function generateId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function isMarketOpen() {
    const now = new Date();
    const currentTime = now.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour12: false
    });
    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
    const isWithinTradingHours = currentTime >= '09:15:00' && currentTime <= '15:30:00';
    return isWeekday && isWithinTradingHours;
}
function safeJsonParse(jsonString, defaultValue) {
    try {
        return JSON.parse(jsonString);
    } catch  {
        return defaultValue;
    }
}
}}),
"[project]/src/lib/utils/symbolMapping.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Symbol to token mapping for Angel One API
// This maps trading symbols to their corresponding Angel One tokens
__turbopack_context__.s({
    "INDEX_MAPPINGS": (()=>INDEX_MAPPINGS),
    "STOCK_MAPPINGS": (()=>STOCK_MAPPINGS),
    "getSupportedIndices": (()=>getSupportedIndices),
    "getSupportedStocks": (()=>getSupportedStocks),
    "getSupportedSymbols": (()=>getSupportedSymbols),
    "isSymbolSupported": (()=>isSymbolSupported),
    "resolveIndexSymbol": (()=>resolveIndexSymbol),
    "resolveStockSymbol": (()=>resolveStockSymbol),
    "resolveSymbol": (()=>resolveSymbol)
});
const STOCK_MAPPINGS = {
    'SBIN-EQ': {
        symbol: 'SBIN-EQ',
        name: 'State Bank of India',
        token: '3045',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'RELIANCE-EQ': {
        symbol: 'RELIANCE-EQ',
        name: 'Reliance Industries',
        token: '2885',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'TCS-EQ': {
        symbol: 'TCS-EQ',
        name: 'Tata Consultancy Services',
        token: '11536',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'INFY-EQ': {
        symbol: 'INFY-EQ',
        name: 'Infosys Limited',
        token: '1594',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'HDFCBANK-EQ': {
        symbol: 'HDFCBANK-EQ',
        name: 'HDFC Bank',
        token: '1333',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'ICICIBANK-EQ': {
        symbol: 'ICICIBANK-EQ',
        name: 'ICICI Bank',
        token: '4963',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'BHARTIARTL-EQ': {
        symbol: 'BHARTIARTL-EQ',
        name: 'Bharti Airtel',
        token: '10604',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'ITC-EQ': {
        symbol: 'ITC-EQ',
        name: 'ITC Limited',
        token: '424',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'KOTAKBANK-EQ': {
        symbol: 'KOTAKBANK-EQ',
        name: 'Kotak Mahindra Bank',
        token: '1922',
        exchange: 'NSE',
        type: 'STOCK'
    },
    'LT-EQ': {
        symbol: 'LT-EQ',
        name: 'Larsen & Toubro',
        token: '11483',
        exchange: 'NSE',
        type: 'STOCK'
    }
};
const INDEX_MAPPINGS = {
    'NIFTY': {
        symbol: 'NIFTY',
        name: 'Nifty 50',
        token: '********',
        exchange: 'NSE',
        type: 'INDEX'
    },
    'BANKNIFTY': {
        symbol: 'BANKNIFTY',
        name: 'Bank Nifty',
        token: '********',
        exchange: 'NSE',
        type: 'INDEX'
    },
    'NIFTYIT': {
        symbol: 'NIFTYIT',
        name: 'Nifty IT',
        token: '********',
        exchange: 'NSE',
        type: 'INDEX'
    },
    'SENSEX': {
        symbol: 'SENSEX',
        name: 'BSE Sensex',
        token: '********',
        exchange: 'BSE',
        type: 'INDEX'
    },
    'NIFTYNEXT50': {
        symbol: 'NIFTYNEXT50',
        name: 'Nifty Next 50',
        token: '********',
        exchange: 'NSE',
        type: 'INDEX'
    }
};
function resolveStockSymbol(symbol) {
    const mapping = STOCK_MAPPINGS[symbol];
    if (!mapping) {
        throw new Error(`Unknown stock symbol: ${symbol}. Please use a supported symbol or add it to the mapping.`);
    }
    return mapping;
}
function resolveIndexSymbol(symbol) {
    const mapping = INDEX_MAPPINGS[symbol];
    if (!mapping) {
        throw new Error(`Unknown index symbol: ${symbol}. Please use a supported index or add it to the mapping.`);
    }
    return mapping;
}
function resolveSymbol(symbol) {
    // Try stock mapping first
    if (symbol in STOCK_MAPPINGS) {
        return STOCK_MAPPINGS[symbol];
    }
    // Try index mapping
    if (symbol in INDEX_MAPPINGS) {
        return INDEX_MAPPINGS[symbol];
    }
    throw new Error(`Unknown symbol: ${symbol}. Please use a supported stock or index symbol.`);
}
function isSymbolSupported(symbol) {
    return symbol in STOCK_MAPPINGS || symbol in INDEX_MAPPINGS;
}
function getSupportedSymbols() {
    return [
        ...Object.values(STOCK_MAPPINGS),
        ...Object.values(INDEX_MAPPINGS)
    ];
}
function getSupportedStocks() {
    return Object.values(STOCK_MAPPINGS);
}
function getSupportedIndices() {
    return Object.values(INDEX_MAPPINGS);
}
}}),
"[project]/src/lib/services/stockData.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Stock data service for the What If investment analysis tool
__turbopack_context__.s({
    "StockDataService": (()=>StockDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$symbolMapping$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/symbolMapping.ts [app-route] (ecmascript)");
;
;
;
class StockDataService {
    angelOneClient;
    constructor(angelOneClient){
        this.angelOneClient = angelOneClient;
    }
    /**
   * Get historical price data for a stock with automatic chunking for large date ranges
   */ async getHistoricalPrices(symbolToken, exchange, startDate, endDate, interval = 'ONE_DAY') {
        // Calculate date range in days
        const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        // Basic validation (without strict date range limits since we have chunking)
        const basicValidation = this.validateBasicDateRange(startDate, endDate);
        if (!basicValidation.isValid) {
            throw new Error(basicValidation.error);
        }
        // If date range is larger than 2 years (730 days), chunk the requests
        const MAX_DAYS_PER_CHUNK = 730; // 2 years
        if (daysDifference <= MAX_DAYS_PER_CHUNK) {
            // Single request for smaller ranges - apply full validation
            const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateDateRange"])(startDate, endDate);
            if (!validation.isValid) {
                throw new Error(validation.error);
            }
            return this.fetchSingleDateRange(symbolToken, exchange, startDate, endDate, interval);
        } else {
            // Chunk large date ranges - allow larger ranges with chunking
            console.log(`📅 Large date range detected (${daysDifference} days). Chunking into smaller requests...`);
            return this.fetchChunkedDateRange(symbolToken, exchange, startDate, endDate, interval, MAX_DAYS_PER_CHUNK);
        }
    }
    /**
   * Basic date validation without strict range limits
   */ validateBasicDateRange(startDate, endDate) {
        const now = new Date();
        if (startDate >= endDate) {
            return {
                isValid: false,
                error: 'Start date must be before end date'
            };
        }
        if (startDate > now) {
            return {
                isValid: false,
                error: 'Start date cannot be in the future'
            };
        }
        if (endDate > now) {
            return {
                isValid: false,
                error: 'End date cannot be in the future'
            };
        }
        // Angel One API historical data limits (conservative)
        const angelOneMinDate = new Date('2018-01-01');
        if (startDate < angelOneMinDate) {
            return {
                isValid: false,
                error: `Start date cannot be earlier than ${angelOneMinDate.toLocaleDateString()}. Angel One API historical data is limited to recent years.`
            };
        }
        // Minimum date range validation
        const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const minDays = 30; // At least 1 month for meaningful analysis
        if (daysDifference < minDays) {
            return {
                isValid: false,
                error: `Date range too small (${daysDifference} days). Minimum required is ${minDays} days for meaningful analysis.`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Fetch historical data for a single date range
   */ async fetchSingleDateRange(symbolToken, exchange, startDate, endDate, interval) {
        const fromdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(startDate, 'yyyy-MM-dd HH:mm');
        const todate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(endDate, 'yyyy-MM-dd HH:mm');
        const request = {
            exchange,
            symboltoken: symbolToken,
            interval,
            fromdate,
            todate
        };
        try {
            console.log(`📊 Fetching data: ${fromdate} to ${todate}`);
            const historicalData = await this.angelOneClient.getHistoricalData(request);
            // Sort by date ascending
            return historicalData.sort((a, b)=>a.date.getTime() - b.date.getTime());
        } catch (error) {
            console.error('Error fetching historical data:', error);
            throw new Error(`Failed to fetch historical data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetch historical data in chunks for large date ranges
   */ async fetchChunkedDateRange(symbolToken, exchange, startDate, endDate, interval, maxDaysPerChunk) {
        const allData = [];
        let currentStart = new Date(startDate);
        let chunkNumber = 1;
        while(currentStart < endDate){
            // Calculate chunk end date
            const chunkEnd = new Date(currentStart);
            chunkEnd.setDate(chunkEnd.getDate() + maxDaysPerChunk);
            // Don't exceed the actual end date
            if (chunkEnd > endDate) {
                chunkEnd.setTime(endDate.getTime());
            }
            console.log(`📦 Fetching chunk ${chunkNumber}: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(currentStart, 'yyyy-MM-dd')} to ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(chunkEnd, 'yyyy-MM-dd')}`);
            try {
                const chunkData = await this.fetchSingleDateRange(symbolToken, exchange, currentStart, chunkEnd, interval);
                allData.push(...chunkData);
                // Add a small delay between requests to avoid rate limiting
                await new Promise((resolve)=>setTimeout(resolve, 100));
            } catch (error) {
                console.error(`Error fetching chunk ${chunkNumber}:`, error);
                // Continue with next chunk instead of failing completely
                console.log(`⚠️ Skipping chunk ${chunkNumber} due to error, continuing...`);
            }
            // Move to next chunk
            currentStart = new Date(chunkEnd);
            currentStart.setDate(currentStart.getDate() + 1);
            chunkNumber++;
        }
        if (allData.length === 0) {
            throw new Error('No historical data could be fetched for any date range');
        }
        console.log(`✅ Successfully fetched ${allData.length} data points across ${chunkNumber - 1} chunks`);
        // Remove duplicates and sort by date
        const uniqueData = allData.filter((item, index, arr)=>index === arr.findIndex((other)=>other.date.getTime() === item.date.getTime()));
        return uniqueData.sort((a, b)=>a.date.getTime() - b.date.getTime());
    }
    /**
   * Get current stock price
   */ async getCurrentPrice(tradingSymbol, symbolToken, exchange) {
        const request = {
            exchange,
            tradingsymbol: tradingSymbol,
            symboltoken: symbolToken
        };
        try {
            return await this.angelOneClient.getCurrentPrice(request);
        } catch (error) {
            console.error('Error fetching current price:', error);
            throw new Error(`Failed to fetch current price: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Calculate investment result for a scenario
   */ async calculateInvestmentResult(scenario) {
        try {
            // Resolve stock symbol to token and exchange
            const stockMapping = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$symbolMapping$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveStockSymbol"])(scenario.stockSymbol);
            console.log(`📊 Resolved ${scenario.stockSymbol} to token ${stockMapping.token} on ${stockMapping.exchange}`);
            // Get historical data for the investment period
            const historicalData = await this.getHistoricalPrices(stockMapping.token, stockMapping.exchange, scenario.startDate, scenario.endDate);
            if (historicalData.length === 0) {
                throw new Error('No historical data available for the specified period');
            }
            // Get start and end prices
            const startPrice = historicalData[0].close;
            const endPrice = historicalData[historicalData.length - 1].close;
            // Calculate number of shares that could be bought
            const numberOfShares = scenario.investmentAmount / startPrice;
            // Calculate current value
            const currentValue = numberOfShares * endPrice;
            // Calculate returns
            const absoluteReturn = (currentValue - scenario.investmentAmount) / scenario.investmentAmount * 100;
            const years = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateYearsBetweenDates"])(scenario.startDate, scenario.endDate);
            const cagr = years > 0 ? (Math.pow(currentValue / scenario.investmentAmount, 1 / years) - 1) * 100 : 0;
            return {
                scenario,
                initialValue: scenario.investmentAmount,
                currentValue,
                absoluteReturn,
                cagr: Number(cagr.toFixed(2)),
                totalReturn: currentValue - scenario.investmentAmount,
                annualizedReturn: cagr
            };
        } catch (error) {
            console.error('Error calculating investment result:', error);
            throw new Error(`Failed to calculate investment result: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Get price at a specific date (or closest available date)
   */ async getPriceAtDate(symbolToken, exchange, targetDate) {
        try {
            // Get data for a small range around the target date
            const startDate = new Date(targetDate);
            startDate.setDate(startDate.getDate() - 5); // 5 days before
            const endDate = new Date(targetDate);
            endDate.setDate(endDate.getDate() + 5); // 5 days after
            const historicalData = await this.getHistoricalPrices(symbolToken, exchange, startDate, endDate);
            if (historicalData.length === 0) {
                return null;
            }
            // Find the closest date
            let closestData = historicalData[0];
            let minDiff = Math.abs(closestData.date.getTime() - targetDate.getTime());
            for (const data of historicalData){
                const diff = Math.abs(data.date.getTime() - targetDate.getTime());
                if (diff < minDiff) {
                    minDiff = diff;
                    closestData = data;
                }
            }
            return {
                price: closestData.close,
                actualDate: closestData.date
            };
        } catch (error) {
            console.error('Error getting price at date:', error);
            return null;
        }
    }
    /**
   * Validate stock symbol and get basic info
   */ async validateStock(tradingSymbol, symbolToken, exchange) {
        try {
            const stockData = await this.getCurrentPrice(tradingSymbol, symbolToken, exchange);
            return {
                isValid: true,
                stockData
            };
        } catch (error) {
            return {
                isValid: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    /**
   * Get multiple stocks' current prices
   */ async getMultipleCurrentPrices(stocks) {
        const results = [];
        // Process stocks sequentially to respect rate limits
        for (const stock of stocks){
            try {
                const stockData = await this.getCurrentPrice(stock.tradingSymbol, stock.symbolToken, stock.exchange);
                results.push(stockData);
            } catch (error) {
                console.error(`Error fetching price for ${stock.tradingSymbol}:`, error);
            // Continue with other stocks even if one fails
            }
        }
        return results;
    }
}
}}),
"[project]/src/lib/services/benchmarkData.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Benchmark data service for the What If investment analysis tool
// Now uses real market indices instead of simulated FD/Gold data
__turbopack_context__.s({
    "BenchmarkDataService": (()=>BenchmarkDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$symbolMapping$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/symbolMapping.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-route] (ecmascript) <locals>");
;
;
;
class BenchmarkDataService {
    angelOneClient;
    constructor(angelOneClient){
        this.angelOneClient = angelOneClient;
    }
    /**
   * Get real market index data (replaces simulated FD/Gold)
   */ async getIndexData(indexSymbol, startDate, endDate) {
        try {
            const indexMapping = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$symbolMapping$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveIndexSymbol"])(indexSymbol);
            const request = {
                exchange: indexMapping.exchange,
                symboltoken: indexMapping.token,
                interval: 'ONE_DAY',
                fromdate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(startDate, 'yyyy-MM-dd HH:mm'),
                todate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(endDate, 'yyyy-MM-dd HH:mm')
            };
            console.log(`📊 Fetching ${indexMapping.name} data from ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(startDate, 'yyyy-MM-dd')} to ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(endDate, 'yyyy-MM-dd')}`);
            const historicalData = await this.angelOneClient.getHistoricalData(request);
            if (historicalData.length === 0) {
                throw new Error(`No historical data available for ${indexMapping.name}`);
            }
            const startPrice = historicalData[0].close;
            const endPrice = historicalData[historicalData.length - 1].close;
            const years = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateYearsBetweenDates"])(startDate, endDate);
            return {
                type: indexSymbol,
                name: indexMapping.name,
                startDate,
                endDate,
                startValue: startPrice,
                endValue: endPrice,
                cagr: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateCAGR"])(startPrice, endPrice, years),
                absoluteReturn: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateAbsoluteReturn"])(startPrice, endPrice),
                historicalData: historicalData.map((item)=>({
                        date: item.date,
                        value: item.close
                    }))
            };
        } catch (error) {
            console.error(`Error fetching ${indexSymbol} data:`, error);
            throw new Error(`Failed to fetch ${indexSymbol} data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Get Nifty 50 historical data (updated to use new structure)
   */ async getNiftyData(startDate, endDate) {
        return this.getIndexData('NIFTY', startDate, endDate);
    }
    /**
   * Get Bank Nifty historical data (replaces Gold)
   */ async getBankNiftyData(startDate, endDate) {
        return this.getIndexData('BANKNIFTY', startDate, endDate);
    }
    /**
   * Get Nifty IT historical data (replaces Fixed Deposit)
   */ async getNiftyITData(startDate, endDate) {
        return this.getIndexData('NIFTYIT', startDate, endDate);
    }
    /**
   * Get BSE Sensex historical data (additional benchmark)
   */ async getSensexData(startDate, endDate) {
        return this.getIndexData('SENSEX', startDate, endDate);
    }
    /**
   * Get all benchmark data for comparison (now uses real market indices)
   */ async getAllBenchmarks(startDate, endDate) {
        try {
            console.log('📊 Fetching all benchmark data with real market indices...');
            const [nifty, bankNifty, niftyIT] = await Promise.all([
                this.getNiftyData(startDate, endDate),
                this.getBankNiftyData(startDate, endDate),
                this.getNiftyITData(startDate, endDate)
            ]);
            return {
                nifty,
                bankNifty,
                niftyIT
            };
        } catch (error) {
            console.error('Error fetching benchmark data:', error);
            throw new Error(`Failed to fetch benchmark data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Calculate benchmark returns for a given investment amount and period
   * Now supports real market indices instead of simulated data
   */ async calculateBenchmarkReturns(benchmarkType, investmentAmount, startDate, endDate) {
        let benchmarkData;
        switch(benchmarkType){
            case 'NIFTY':
                benchmarkData = await this.getNiftyData(startDate, endDate);
                break;
            case 'BANKNIFTY':
                benchmarkData = await this.getBankNiftyData(startDate, endDate);
                break;
            case 'NIFTYIT':
                benchmarkData = await this.getNiftyITData(startDate, endDate);
                break;
            case 'SENSEX':
                benchmarkData = await this.getSensexData(startDate, endDate);
                break;
            default:
                throw new Error(`Unsupported benchmark type: ${benchmarkType}`);
        }
        if (benchmarkData.historicalData.length === 0) {
            throw new Error(`No data available for ${benchmarkType} in the specified period`);
        }
        const startValue = benchmarkData.startValue;
        const endValue = benchmarkData.endValue;
        // Calculate how much of the benchmark could be bought with the investment amount
        const units = investmentAmount / startValue;
        const currentValue = units * endValue;
        return {
            initialValue: investmentAmount,
            currentValue,
            cagr: benchmarkData.cagr,
            absoluteReturn: benchmarkData.absoluteReturn
        };
    }
}
}}),
"[project]/src/lib/services/investmentCalculator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Investment calculation engine for the What If investment analysis tool
__turbopack_context__.s({
    "InvestmentCalculator": (()=>InvestmentCalculator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-route] (ecmascript)");
;
class InvestmentCalculator {
    stockDataService;
    benchmarkDataService;
    constructor(stockDataService, benchmarkDataService){
        this.stockDataService = stockDataService;
        this.benchmarkDataService = benchmarkDataService;
    }
    /**
   * Create a new investment scenario
   */ createScenario(stockSymbol, investmentAmount, startDate, endDate = new Date()) {
        // Validate inputs
        if (investmentAmount <= 0) {
            throw new Error('Investment amount must be greater than 0');
        }
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateDateRange"])(startDate, endDate);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        return {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])(),
            stockSymbol,
            investmentAmount,
            startDate,
            endDate,
            createdAt: new Date()
        };
    }
    /**
   * Calculate investment result for a scenario
   */ async calculateInvestmentResult(scenario) {
        try {
            return await this.stockDataService.calculateInvestmentResult(scenario);
        } catch (error) {
            console.error('Error calculating investment result:', error);
            throw error;
        }
    }
    /**
   * Calculate real benchmark returns using market indices
   */ async calculateRealBenchmarkReturns(investmentAmount, startDate, endDate) {
        const results = {};
        // Use real market indices instead of simulated FD/Gold
        const benchmarks = [
            'NIFTY',
            'BANKNIFTY',
            'NIFTYIT'
        ];
        // Process benchmarks sequentially to avoid overwhelming the API
        for (const benchmark of benchmarks){
            try {
                console.log(`📊 Calculating ${benchmark} benchmark returns...`);
                results[benchmark] = await this.benchmarkDataService.calculateBenchmarkReturns(benchmark, investmentAmount, startDate, endDate);
            } catch (error) {
                console.error(`Error calculating ${benchmark} returns:`, error);
                // Continue with other benchmarks even if one fails
                results[benchmark] = {
                    initialValue: investmentAmount,
                    currentValue: investmentAmount,
                    cagr: 0,
                    absoluteReturn: 0
                };
            }
        }
        return results;
    }
    /**
   * Calculate investment result with benchmark comparisons
   */ async calculateWithComparisons(scenario) {
        try {
            // Calculate the main investment result
            const investmentResult = await this.calculateInvestmentResult(scenario);
            // Calculate benchmark returns using real market indices
            const benchmarkReturns = await this.calculateRealBenchmarkReturns(scenario.investmentAmount, scenario.startDate, scenario.endDate);
            return {
                investment: investmentResult,
                benchmarks: benchmarkReturns
            };
        } catch (error) {
            console.error('Error calculating investment with comparisons:', error);
            throw error;
        }
    }
    /**
   * Calculate returns for multiple investment amounts (sensitivity analysis)
   */ async calculateSensitivityAnalysis(stockSymbol, baseAmount, startDate, endDate, variations = [
        0.5,
        0.75,
        1,
        1.25,
        1.5,
        2
    ] // Multipliers
    ) {
        const results = [];
        for (const multiplier of variations){
            const amount = baseAmount * multiplier;
            const scenario = this.createScenario(stockSymbol, amount, startDate, endDate);
            try {
                const result = await this.calculateInvestmentResult(scenario);
                results.push({
                    amount,
                    result
                });
            } catch (error) {
                console.error(`Error calculating for amount ${amount}:`, error);
            // Continue with other amounts
            }
        }
        return results;
    }
    /**
   * Calculate returns for different time periods (time-based analysis)
   */ async calculateTimeBasedAnalysis(stockSymbol, investmentAmount, baseStartDate, periods = [
        {
            label: '6 months',
            months: 6
        },
        {
            label: '1 year',
            months: 12
        },
        {
            label: '2 years',
            months: 24
        },
        {
            label: '3 years',
            months: 36
        },
        {
            label: '5 years',
            months: 60
        }
    ]) {
        const results = [];
        for (const period of periods){
            const startDate = new Date(baseStartDate);
            const endDate = new Date(baseStartDate);
            endDate.setMonth(endDate.getMonth() + period.months);
            // Don't calculate for future dates
            if (endDate > new Date()) {
                continue;
            }
            const scenario = this.createScenario(stockSymbol, investmentAmount, startDate, endDate);
            try {
                const result = await this.calculateInvestmentResult(scenario);
                results.push({
                    period: period.label,
                    startDate,
                    endDate,
                    result
                });
            } catch (error) {
                console.error(`Error calculating for period ${period.label}:`, error);
            // Continue with other periods
            }
        }
        return results;
    }
    /**
   * Calculate SIP (Systematic Investment Plan) returns
   */ async calculateSIPReturns(stockSymbol, monthlyAmount, startDate, endDate) {
        const installments = [];
        let totalInvested = 0;
        let totalUnits = 0;
        const currentDate = new Date(startDate);
        // Calculate monthly investments
        while(currentDate <= endDate){
            try {
                // Get price at this date (or closest available)
                const priceData = await this.stockDataService.getPriceAtDate(stockSymbol, 'NSE', currentDate);
                if (priceData) {
                    const units = monthlyAmount / priceData.price;
                    totalUnits += units;
                    totalInvested += monthlyAmount;
                    installments.push({
                        date: new Date(currentDate),
                        amount: monthlyAmount,
                        price: priceData.price,
                        units,
                        cumulativeUnits: totalUnits,
                        cumulativeInvestment: totalInvested
                    });
                }
            } catch (error) {
                console.error(`Error calculating SIP for ${currentDate}:`, error);
            // Continue with next month
            }
            // Move to next month
            currentDate.setMonth(currentDate.getMonth() + 1);
        }
        if (installments.length === 0) {
            throw new Error('No SIP installments could be calculated');
        }
        // Get current price to calculate current value
        try {
            const currentPriceData = await this.stockDataService.getCurrentPrice(stockSymbol.split('-')[0], stockSymbol, 'NSE');
            const currentValue = totalUnits * currentPriceData.currentPrice;
            const totalReturn = currentValue - totalInvested;
            const years = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateYearsBetweenDates"])(startDate, endDate);
            const cagr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateCAGR"])(totalInvested, currentValue, years);
            const absoluteReturn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateAbsoluteReturn"])(totalInvested, currentValue);
            return {
                totalInvested,
                currentValue,
                totalReturn,
                cagr,
                absoluteReturn,
                installments
            };
        } catch (error) {
            throw new Error(`Failed to calculate SIP returns: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Calculate optimal investment timing (dollar-cost averaging analysis)
   */ async calculateOptimalTiming(stockSymbol, totalAmount, startDate, endDate, strategies = [
        {
            name: 'Lump Sum at Start',
            type: 'lump_sum'
        },
        {
            name: 'Monthly SIP',
            type: 'monthly_sip'
        },
        {
            name: 'Quarterly SIP',
            type: 'quarterly_sip'
        }
    ]) {
        const results = [];
        for (const strategy of strategies){
            try {
                let result;
                switch(strategy.type){
                    case 'lump_sum':
                        const scenario = this.createScenario(stockSymbol, totalAmount, startDate, endDate);
                        const lumpSumResult = await this.calculateInvestmentResult(scenario);
                        result = {
                            strategy: strategy.name,
                            totalInvested: lumpSumResult.initialValue,
                            currentValue: lumpSumResult.currentValue,
                            cagr: lumpSumResult.cagr,
                            absoluteReturn: lumpSumResult.absoluteReturn
                        };
                        break;
                    case 'monthly_sip':
                        const months = Math.ceil((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateYearsBetweenDates"])(startDate, endDate) * 12);
                        const monthlyAmount = totalAmount / months;
                        const monthlySipResult = await this.calculateSIPReturns(stockSymbol, monthlyAmount, startDate, endDate);
                        result = {
                            strategy: strategy.name,
                            totalInvested: monthlySipResult.totalInvested,
                            currentValue: monthlySipResult.currentValue,
                            cagr: monthlySipResult.cagr,
                            absoluteReturn: monthlySipResult.absoluteReturn
                        };
                        break;
                    case 'quarterly_sip':
                        const quarters = Math.ceil((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateYearsBetweenDates"])(startDate, endDate) * 4);
                        const quarterlyAmount = totalAmount / quarters;
                        // For quarterly, we'll simulate with 3-month intervals
                        const quarterlySipResult = await this.calculateSIPReturns(stockSymbol, quarterlyAmount, startDate, endDate);
                        result = {
                            strategy: strategy.name,
                            totalInvested: quarterlySipResult.totalInvested,
                            currentValue: quarterlySipResult.currentValue,
                            cagr: quarterlySipResult.cagr,
                            absoluteReturn: quarterlySipResult.absoluteReturn
                        };
                        break;
                    default:
                        continue;
                }
                results.push(result);
            } catch (error) {
                console.error(`Error calculating ${strategy.name}:`, error);
            // Continue with other strategies
            }
        }
        return results;
    }
}
}}),
"[project]/src/lib/services/comparisonService.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Comparison service for the What If investment analysis tool
__turbopack_context__.s({
    "ComparisonService": (()=>ComparisonService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-route] (ecmascript)");
;
class ComparisonService {
    investmentCalculator;
    benchmarkDataService;
    constructor(investmentCalculator, benchmarkDataService){
        this.investmentCalculator = investmentCalculator;
        this.benchmarkDataService = benchmarkDataService;
    }
    /**
   * Generate comprehensive comparison summary
   */ async generateComparisonSummary(scenario) {
        try {
            const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);
            // Prepare investment data
            const investment = {
                name: `${scenario.stockSymbol} Investment`,
                initialValue: comparisonResult.investment.initialValue,
                currentValue: comparisonResult.investment.currentValue,
                totalReturn: comparisonResult.investment.totalReturn,
                cagr: comparisonResult.investment.cagr,
                absoluteReturn: comparisonResult.investment.absoluteReturn,
                rank: 0
            };
            // Prepare benchmark data
            const benchmarks = Object.entries(comparisonResult.benchmarks).map(([type, data])=>({
                    name: this.getBenchmarkDisplayName(type),
                    type,
                    initialValue: data.initialValue,
                    currentValue: data.currentValue,
                    totalReturn: data.currentValue - data.initialValue,
                    cagr: data.cagr,
                    absoluteReturn: data.absoluteReturn,
                    rank: 0,
                    outperformance: investment.cagr - data.cagr
                }));
            // Calculate rankings based on CAGR
            const allInvestments = [
                investment,
                ...benchmarks
            ];
            allInvestments.sort((a, b)=>b.cagr - a.cagr);
            allInvestments.forEach((item, index)=>{
                item.rank = index + 1;
            });
            // Generate insights
            const insights = this.generateInsights(investment, benchmarks, scenario);
            return {
                investment,
                benchmarks,
                insights
            };
        } catch (error) {
            console.error('Error generating comparison summary:', error);
            throw error;
        }
    }
    /**
   * Calculate performance metrics across all investments
   */ calculatePerformanceMetrics(comparisonSummary) {
        const allInvestments = [
            comparisonSummary.investment,
            ...comparisonSummary.benchmarks
        ];
        // Find best and worst performers
        const sortedByCagr = [
            ...allInvestments
        ].sort((a, b)=>b.cagr - a.cagr);
        const bestPerformer = {
            name: sortedByCagr[0].name,
            cagr: sortedByCagr[0].cagr,
            absoluteReturn: sortedByCagr[0].absoluteReturn
        };
        const worstPerformer = {
            name: sortedByCagr[sortedByCagr.length - 1].name,
            cagr: sortedByCagr[sortedByCagr.length - 1].cagr,
            absoluteReturn: sortedByCagr[sortedByCagr.length - 1].absoluteReturn
        };
        // Calculate average CAGR
        const averageCagr = allInvestments.reduce((sum, inv)=>sum + inv.cagr, 0) / allInvestments.length;
        // Calculate volatility ranking (simplified - based on absolute return variance from CAGR)
        const volatilityRanking = allInvestments.map((inv)=>({
                name: inv.name,
                volatility: Math.abs(inv.absoluteReturn - inv.cagr)
            })).sort((a, b)=>a.volatility - b.volatility);
        return {
            bestPerformer,
            worstPerformer,
            averageCagr: Number(averageCagr.toFixed(2)),
            volatilityRanking
        };
    }
    /**
   * Generate chart data for comparison visualization
   */ async generateComparisonChartData(scenario) {
        try {
            const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);
            // For time series, we'll need to get historical data for all benchmarks
            // This is a simplified version - in production, you'd want actual time series data
            const timeSeriesData = await this.generateTimeSeriesData(scenario, comparisonResult);
            // Bar chart data for final comparison
            const barChartData = [
                {
                    name: scenario.stockSymbol,
                    cagr: comparisonResult.investment.cagr,
                    absoluteReturn: comparisonResult.investment.absoluteReturn,
                    currentValue: comparisonResult.investment.currentValue
                },
                ...Object.entries(comparisonResult.benchmarks).map(([type, data])=>({
                        name: this.getBenchmarkDisplayName(type),
                        cagr: data.cagr,
                        absoluteReturn: data.absoluteReturn,
                        currentValue: data.currentValue
                    }))
            ];
            return {
                timeSeriesData,
                barChartData
            };
        } catch (error) {
            console.error('Error generating chart data:', error);
            throw error;
        }
    }
    /**
   * Compare multiple stocks against benchmarks
   */ async compareMultipleStocks(scenarios) {
        const results = [];
        for (const scenario of scenarios){
            try {
                const summary = await this.generateComparisonSummary(scenario);
                results.push({
                    scenario,
                    summary
                });
            } catch (error) {
                console.error(`Error comparing scenario ${scenario.id}:`, error);
            // Continue with other scenarios
            }
        }
        return results;
    }
    /**
   * Generate insights based on comparison results
   */ generateInsights(investment, benchmarks, scenario) {
        const insights = [];
        const years = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateYearsBetweenDates"])(scenario.startDate, scenario.endDate);
        // Performance insights
        if (investment.rank === 1) {
            insights.push(`🎉 Your ${scenario.stockSymbol} investment outperformed all benchmarks with a CAGR of ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(investment.cagr)}.`);
        } else {
            const betterBenchmarks = benchmarks.filter((b)=>b.rank < investment.rank);
            if (betterBenchmarks.length > 0) {
                const bestBenchmark = betterBenchmarks[0];
                insights.push(`📊 Your investment ranked #${investment.rank}. ${bestBenchmark.name} performed better with ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(bestBenchmark.cagr)} CAGR.`);
            }
        }
        // Market comparison insights (updated for real indices)
        const niftyBenchmark = benchmarks.find((b)=>b.type === 'NIFTY');
        const bankNiftyBenchmark = benchmarks.find((b)=>b.type === 'BANKNIFTY');
        const niftyITBenchmark = benchmarks.find((b)=>b.type === 'NIFTYIT');
        if (niftyBenchmark) {
            if (investment.cagr > niftyBenchmark.cagr) {
                insights.push(`📈 Your stock selection beat the market (Nifty 50) by ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(investment.cagr - niftyBenchmark.cagr)} annually.`);
            } else {
                insights.push(`📉 The market (Nifty 50) outperformed your stock by ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(niftyBenchmark.cagr - investment.cagr)} annually.`);
            }
        }
        if (bankNiftyBenchmark) {
            if (investment.cagr > bankNiftyBenchmark.cagr) {
                insights.push(`🏦 Your investment outperformed the banking sector (Bank Nifty) by ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(investment.cagr - bankNiftyBenchmark.cagr)} annually.`);
            } else {
                insights.push(`🏦 The banking sector (Bank Nifty) outperformed your stock by ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(bankNiftyBenchmark.cagr - investment.cagr)} annually.`);
            }
        }
        if (niftyITBenchmark) {
            if (investment.cagr > niftyITBenchmark.cagr) {
                insights.push(`💻 Your investment beat the IT sector (Nifty IT) by ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(investment.cagr - niftyITBenchmark.cagr)} annually.`);
            } else {
                insights.push(`💻 The IT sector (Nifty IT) outperformed your stock by ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(niftyITBenchmark.cagr - investment.cagr)} annually.`);
            }
        }
        // Time-based insights
        if (years >= 5) {
            insights.push(`⏰ Over ${Math.round(years)} years, your ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatCurrency"])(scenario.investmentAmount)} investment grew to ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatCurrency"])(investment.currentValue)}.`);
        } else if (years >= 1) {
            insights.push(`📅 In ${Math.round(years * 12)} months, your investment generated ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatCurrency"])(investment.totalReturn)} in returns.`);
        }
        // Value insights
        if (investment.absoluteReturn > 100) {
            insights.push(`💰 Your investment more than doubled your money with ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(investment.absoluteReturn)} total returns.`);
        } else if (investment.absoluteReturn > 50) {
            insights.push(`💵 Your investment generated strong returns of ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(investment.absoluteReturn)}.`);
        } else if (investment.absoluteReturn < 0) {
            insights.push(`⚠️ Your investment resulted in a loss of ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatPercentage"])(Math.abs(investment.absoluteReturn))}.`);
        }
        return insights;
    }
    /**
   * Get display name for benchmark type (updated for real market indices)
   */ getBenchmarkDisplayName(type) {
        const names = {
            'NIFTY': 'Nifty 50',
            'BANKNIFTY': 'Bank Nifty',
            'NIFTYIT': 'Nifty IT',
            'SENSEX': 'BSE Sensex',
            'NIFTYNEXT50': 'Nifty Next 50'
        };
        return names[type] || type;
    }
    /**
   * Generate time series data for visualization
   */ async generateTimeSeriesData(scenario, comparisonResult) {
        // This is a simplified implementation
        // In production, you'd fetch actual historical data for all assets
        const data = [];
        const startDate = new Date(scenario.startDate);
        const endDate = new Date(scenario.endDate);
        const currentDate = new Date(startDate);
        // Calculate daily growth rates
        const years = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateYearsBetweenDates"])(startDate, endDate);
        const days = years * 365;
        const investmentDailyGrowth = Math.pow(comparisonResult.investment.currentValue / comparisonResult.investment.initialValue, 1 / days);
        const goldDailyGrowth = Math.pow(comparisonResult.benchmarks.GOLD.currentValue / comparisonResult.benchmarks.GOLD.initialValue, 1 / days);
        const fdDailyGrowth = Math.pow(comparisonResult.benchmarks.FD.currentValue / comparisonResult.benchmarks.FD.initialValue, 1 / days);
        const niftyDailyGrowth = Math.pow(comparisonResult.benchmarks.NIFTY.currentValue / comparisonResult.benchmarks.NIFTY.initialValue, 1 / days);
        let dayCount = 0;
        while(currentDate <= endDate){
            const investmentValue = scenario.investmentAmount * Math.pow(investmentDailyGrowth, dayCount);
            const goldValue = scenario.investmentAmount * Math.pow(goldDailyGrowth, dayCount);
            const fdValue = scenario.investmentAmount * Math.pow(fdDailyGrowth, dayCount);
            const niftyValue = scenario.investmentAmount * Math.pow(niftyDailyGrowth, dayCount);
            data.push({
                date: currentDate.toISOString().split('T')[0],
                investment: Math.round(investmentValue),
                gold: Math.round(goldValue),
                fd: Math.round(fdValue),
                nifty: Math.round(niftyValue)
            });
            currentDate.setDate(currentDate.getDate() + 7); // Weekly data points
            dayCount += 7;
        }
        return data;
    }
}
}}),
"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$angelone$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/angelone.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$stockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/stockData.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$benchmarkData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/benchmarkData.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$investmentCalculator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/investmentCalculator.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$comparisonService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/comparisonService.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
// Initialize services
let angelOneClient;
let stockDataService;
let benchmarkDataService;
let investmentCalculator;
let comparisonService;
async function initializeServices() {
    if (!angelOneClient) {
        console.log('🔧 Creating Angel One client...');
        // Check environment variables
        const requiredEnvVars = {
            apiKey: process.env.ANGEL_ONE_API_KEY,
            clientId: process.env.ANGEL_ONE_CLIENT_ID,
            password: process.env.ANGEL_ONE_PASSWORD,
            totpSecret: process.env.ANGEL_ONE_TOTP_SECRET
        };
        console.log('🔍 Environment variables check:', {
            apiKey: requiredEnvVars.apiKey ? 'SET' : 'MISSING',
            clientId: requiredEnvVars.clientId ? 'SET' : 'MISSING',
            password: requiredEnvVars.password ? 'SET' : 'MISSING',
            totpSecret: requiredEnvVars.totpSecret ? 'SET' : 'MISSING'
        });
        // Initialize Angel One client with environment variables
        angelOneClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$angelone$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AngelOneClient"]({
            apiKey: requiredEnvVars.apiKey,
            clientId: requiredEnvVars.clientId,
            password: requiredEnvVars.password,
            totpSecret: requiredEnvVars.totpSecret
        });
        console.log('🔐 Attempting Angel One authentication...');
        // Authenticate
        const loginResult = await angelOneClient.login();
        console.log('🔐 Login result:', loginResult);
        if (!loginResult.success) {
            throw new Error(`Angel One authentication failed: ${loginResult.message}`);
        }
        console.log('✅ Angel One authentication successful');
        // Initialize services
        console.log('🔧 Initializing services...');
        stockDataService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$stockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StockDataService"](angelOneClient);
        benchmarkDataService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$benchmarkData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BenchmarkDataService"](angelOneClient);
        investmentCalculator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$investmentCalculator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InvestmentCalculator"](stockDataService, benchmarkDataService);
        comparisonService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$comparisonService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ComparisonService"](investmentCalculator, benchmarkDataService);
        console.log('✅ All services initialized successfully');
    }
}
async function POST(request) {
    try {
        console.log('🚀 Starting investment analysis...');
        // Initialize services if not already done
        console.log('🔧 Initializing services...');
        await initializeServices();
        console.log('✅ Services initialized successfully');
        // Parse request body
        console.log('📥 Parsing request body...');
        const body = await request.json();
        const { stockSymbol, investmentAmount, startDate, endDate } = body;
        console.log('📊 Request data:', {
            stockSymbol,
            investmentAmount,
            startDate,
            endDate
        });
        // Validate input
        if (!stockSymbol || !investmentAmount || !startDate || !endDate) {
            console.log('❌ Validation failed: Missing required fields');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields'
            }, {
                status: 400
            });
        }
        // Create investment scenario
        const scenario = {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateId"])(),
            stockSymbol,
            investmentAmount: parseFloat(investmentAmount),
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            createdAt: new Date()
        };
        // Validate date range
        if (scenario.startDate >= scenario.endDate) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Start date must be before end date'
            }, {
                status: 400
            });
        }
        if (scenario.startDate > new Date()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Start date cannot be in the future'
            }, {
                status: 400
            });
        }
        // Calculate investment result
        console.log('📊 Calculating investment result...');
        const investmentResult = await investmentCalculator.calculateInvestmentResult(scenario);
        console.log('✅ Investment result calculated');
        // Calculate comparison with benchmarks
        console.log('📊 Calculating benchmark comparisons...');
        const comparisonResult = await investmentCalculator.calculateWithComparisons(scenario);
        console.log('✅ Benchmark comparisons calculated');
        // Generate comparison summary
        console.log('📊 Generating comparison summary...');
        const comparisonSummary = await comparisonService.generateComparisonSummary(scenario);
        console.log('✅ Comparison summary generated');
        // Generate chart data
        console.log('📊 Generating chart data...');
        const chartData = await comparisonService.generateComparisonChartData(scenario);
        console.log('✅ Chart data generated');
        // Return results
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            investmentResult,
            comparisonResult,
            comparisonSummary,
            chartData
        });
    } catch (error) {
        console.error('💥 Analysis API error:', error);
        console.error('Error type:', error?.constructor?.name);
        console.error('Error message:', error?.message);
        console.error('Error stack:', error?.stack);
        // Handle specific error types
        if (error instanceof Error) {
            console.log('🔍 Checking error type:', error.message);
            if (error.message.includes('Stock not found') || error.message.includes('Invalid stock symbol')) {
                console.log('❌ Stock not found error');
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Stock symbol not found. Please check the symbol and try again.'
                }, {
                    status: 404
                });
            }
            if (error.message.includes('Rate limit')) {
                console.log('❌ Rate limit error');
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Too many requests. Please wait a moment and try again.'
                }, {
                    status: 429
                });
            }
            if (error.message.includes('Authentication')) {
                console.log('❌ Authentication error');
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Authentication failed. Please try again later.'
                }, {
                    status: 401
                });
            }
        }
        console.log('❌ Generic error, returning 500');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'An error occurred while analyzing the investment. Please try again.',
            details: error?.message
        }, {
            status: 500
        });
    }
}
async function GET() {
    try {
        await initializeServices();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: 'OK',
            message: 'Analysis API is running'
        });
    } catch (error) {
        console.error('Health check failed:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: 'ERROR',
            message: 'Service unavailable'
        }, {
            status: 503
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__34facc85._.js.map