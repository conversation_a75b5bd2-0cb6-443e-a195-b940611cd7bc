'use client';

import { InvestmentResult, ComparisonResult } from '@/lib/types';
import ComparisonTable from '@/components/tables/ComparisonTable';
import TimeSeriesChart from '@/components/charts/TimeSeriesChart';
import ComparisonBarChart from '@/components/charts/ComparisonBarChart';
import PerformanceMetrics from '@/components/charts/PerformanceMetrics';
import { formatCurrency, formatPercentage } from '@/lib/utils';

// Share on X functionality
function shareOnX(investmentResult: InvestmentResult, comparisonSummary: any) {
  const stockSymbol = investmentResult.scenario.stockSymbol;
  const cagr = formatPercentage(investmentResult.annualizedReturn);
  const totalReturn = formatCurrency(investmentResult.totalReturn);
  const period = `${investmentResult.scenario.startDate.toLocaleDateString()} - ${investmentResult.scenario.endDate.toLocaleDateString()}`;

  // Generate compelling insights for social sharing
  const insights = [];

  if (comparisonSummary?.investment?.rank === 1) {
    insights.push(`🎉 ${stockSymbol} outperformed ALL benchmarks!`);
  } else {
    insights.push(`📊 ${stockSymbol} ranked #${comparisonSummary?.investment?.rank || 'N/A'} in performance`);
  }

  if (investmentResult.annualizedReturn > 15) {
    insights.push(`🚀 Stellar ${cagr} CAGR!`);
  } else if (investmentResult.annualizedReturn > 10) {
    insights.push(`📈 Strong ${cagr} CAGR`);
  } else if (investmentResult.annualizedReturn > 0) {
    insights.push(`💰 Positive ${cagr} returns`);
  }

  // Create shareable text
  const shareText = `💡 Investment Analysis Results:

${insights.join(' ')}

📊 ${stockSymbol}: ${cagr} CAGR
💵 Total Return: ${totalReturn}
📅 Period: ${period}

Analyzed with real market data 📈

#InvestmentAnalysis #StockMarket #IndianStocks #WealthBuilding`;

  // Create X share URL
  const tweetUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(window.location.href)}`;

  // Open in new window
  window.open(tweetUrl, '_blank', 'width=550,height=420');
}

interface AnalysisResultsProps {
  data: {
    investmentResult: InvestmentResult;
    comparisonResult: ComparisonResult;
    comparisonSummary: any;
    chartData: any;
  };
}

export function AnalysisResults({ data }: AnalysisResultsProps) {
  const { investmentResult, comparisonSummary, chartData } = data;

  return (
    <div className="space-y-8">
      {/* Investment Summary Cards with Glassmorphism */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="glass-card p-6 float-animation">
          <h3 className="text-sm font-medium text-blue-200 mb-2">Initial Investment</h3>
          <p className="text-3xl font-bold text-white">
            {formatCurrency(investmentResult.initialValue)}
          </p>
        </div>

        <div className="glass-card p-6 float-animation" style={{animationDelay: '0.2s'}}>
          <h3 className="text-sm font-medium text-blue-200 mb-2">Current Value</h3>
          <p className="text-3xl font-bold text-green-300">
            {formatCurrency(investmentResult.currentValue)}
          </p>
        </div>

        <div className="glass-card p-6 float-animation" style={{animationDelay: '0.4s'}}>
          <h3 className="text-sm font-medium text-blue-200 mb-2">Total Return</h3>
          <p className={`text-3xl font-bold ${investmentResult.totalReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>
            {formatCurrency(investmentResult.totalReturn)}
          </p>
        </div>

        <div className="glass-card p-6 float-animation" style={{animationDelay: '0.6s'}}>
          <h3 className="text-sm font-medium text-blue-200 mb-2">CAGR</h3>
          <p className={`text-3xl font-bold ${investmentResult.cagr >= 0 ? 'text-green-300' : 'text-red-300'}`}>
            {formatPercentage(investmentResult.cagr)}
          </p>
        </div>
      </div>

      {/* Investment Details with Modern Styling */}
      <div className="glass-card p-8">
        <h3 className="text-2xl font-bold text-white mb-6">Investment Details</h3>
        <div className="mb-6 glass-card p-4 border border-green-400/30 bg-green-500/10">
          <div className="flex items-center gap-3 text-green-200">
            <span className="text-green-400 text-lg">✅</span>
            <span className="font-semibold">Real Market Data:</span>
            <span>Stock prices from Angel One API</span>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-blue-200">Stock Symbol:</span>
              <span className="font-bold text-white text-lg">{investmentResult.scenario.stockSymbol}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-blue-200">Investment Period:</span>
              <span className="font-semibold text-white">
                {new Date(investmentResult.scenario.startDate).toLocaleDateString()} - {new Date(investmentResult.scenario.endDate).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-blue-200">Absolute Return:</span>
              <span className={`font-bold text-lg ${investmentResult.absoluteReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                {formatPercentage(investmentResult.absoluteReturn)}
              </span>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-blue-200">Annualized Return:</span>
              <span className={`font-bold text-lg ${investmentResult.annualizedReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                {formatPercentage(investmentResult.annualizedReturn)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-blue-200">Investment Amount:</span>
              <span className="font-bold text-white text-lg">{formatCurrency(investmentResult.scenario.investmentAmount)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-blue-200">Profit/Loss:</span>
              <span className={`font-bold text-lg ${investmentResult.totalReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                {investmentResult.totalReturn >= 0 ? '+' : ''}{formatCurrency(investmentResult.totalReturn)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Insights with Modern Styling */}
      {comparisonSummary && (
        <div className="glass-card p-8">
          <h3 className="text-2xl font-bold text-white mb-6">Performance Insights</h3>
          <div className="space-y-4">
            {comparisonSummary.insights?.map((insight: string, index: number) => (
              <div key={index} className="flex items-start space-x-4 glass-card p-4 hover:scale-105 transition-transform duration-300">
                <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-white font-medium">{insight}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Charts Section with Modern Styling */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Time Series Chart */}
        {chartData?.timeSeriesData && (
          <div className="glass-card p-8">
            <h3 className="text-2xl font-bold text-white mb-6">📈 Investment Growth Over Time</h3>
            <TimeSeriesChart data={chartData.timeSeriesData} />
          </div>
        )}

        {/* Comparison Bar Chart */}
        {chartData?.barChartData && (
          <div className="glass-card p-8">
            <h3 className="text-2xl font-bold text-white mb-6">📊 Performance Comparison</h3>
            <ComparisonBarChart data={chartData.barChartData} />
          </div>
        )}
      </div>

      {/* Performance Metrics Component */}
      {comparisonSummary && (() => {
        // Create array of all investments including the main investment
        const allInvestments = [
          {
            name: comparisonSummary.investment.name,
            cagr: comparisonSummary.investment.cagr,
            absoluteReturn: comparisonSummary.investment.absoluteReturn,
          },
          ...comparisonSummary.benchmarks.map(b => ({
            name: b.name,
            cagr: b.cagr,
            absoluteReturn: b.absoluteReturn,
          }))
        ];

        // Find actual best and worst performers by CAGR
        const bestPerformer = allInvestments.reduce((best, current) =>
          current.cagr > best.cagr ? current : best
        );

        const worstPerformer = allInvestments.reduce((worst, current) =>
          current.cagr < worst.cagr ? current : worst
        );

        return (
          <div className="glass-card p-8">
            <h3 className="text-2xl font-bold text-white mb-6">📊 Detailed Performance Metrics</h3>
            <PerformanceMetrics
              data={{
                bestPerformer,
                worstPerformer,
                averageCagr: allInvestments.reduce((sum, inv) => sum + inv.cagr, 0) / allInvestments.length,
                volatilityRanking: [
                  { name: 'Bank Nifty', volatility: 15 },
                  { name: 'Nifty IT', volatility: 18 },
                  { name: 'Nifty 50', volatility: 20 },
                  { name: comparisonSummary.investment.name, volatility: 25 },
                ].sort((a, b) => a.volatility - b.volatility),
              }}
              investmentAmount={investmentResult.scenario.investmentAmount}
              investmentPeriod={`${new Date(investmentResult.scenario.startDate).toLocaleDateString()} - ${new Date(investmentResult.scenario.endDate).toLocaleDateString()}`}
            />
          </div>
        );
      })()}

      {/* Comparison Table with Modern Styling */}
      {comparisonSummary && (
        <div className="glass-card p-8">
          <h3 className="text-2xl font-bold text-white mb-6">📊 Benchmark Comparison</h3>
          <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="glass-card p-4 border border-green-400/30 bg-green-500/10">
              <div className="flex items-center gap-3 text-green-200">
                <span className="text-green-400 text-lg">✅</span>
                <span className="font-semibold">Real Market Data:</span>
                <span>All indices from Angel One API</span>
              </div>
            </div>
            <div className="glass-card p-4 border border-blue-400/30 bg-blue-500/10">
              <div className="flex items-center gap-3 text-blue-200">
                <span className="text-blue-400 text-lg">📊</span>
                <span className="font-semibold">Live Benchmarks:</span>
                <span>Nifty 50, Bank Nifty, Nifty IT</span>
              </div>
            </div>
            <div className="glass-card p-4 border border-purple-400/30 bg-purple-500/10">
              <div className="flex items-center gap-3 text-purple-200">
                <span className="text-purple-400 text-lg">🎯</span>
                <span className="font-semibold">Methodology:</span>
                <span>Consistent calculation across all assets</span>
              </div>
            </div>
          </div>
          <ComparisonTable
            data={[comparisonSummary.investment, ...comparisonSummary.benchmarks]}
            investmentName={comparisonSummary.investment.name}
            title="Investment vs Benchmarks"
            showOutperformance={true}
          />
        </div>
      )}

      {/* Benchmark Details */}
      {comparisonSummary && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Benchmark Performance</h3>
          <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center gap-2 text-sm text-yellow-800 dark:text-yellow-200">
              <span className="text-yellow-600">⚠️</span>
              <span className="font-medium">Note:</span>
              <span>Benchmark data uses consistent calculation methodology as comparison table</span>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {comparisonSummary.benchmarks.map((benchmark, index) => (
              <div key={benchmark.name} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {benchmark.name}
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Initial:</span>
                    <span className="text-gray-900 dark:text-white">{formatCurrency(benchmark.initialValue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Current:</span>
                    <span className="text-gray-900 dark:text-white">{formatCurrency(benchmark.currentValue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">CAGR:</span>
                    <span className={`${benchmark.cagr >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {formatPercentage(benchmark.cagr)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Return:</span>
                    <span className={`${benchmark.absoluteReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {formatPercentage(benchmark.absoluteReturn)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Share on X Button */}
      <div className="flex justify-center">
        <button
          onClick={() => shareOnX(investmentResult, comparisonSummary)}
          className="btn-success px-8 py-4 text-lg font-bold rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3"
        >
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
          </svg>
          <span>Share on X</span>
        </button>
      </div>
    </div>
  );
}
