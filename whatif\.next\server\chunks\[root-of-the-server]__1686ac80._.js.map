{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/app/api/analyze-mock/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🧪 Mock analysis API called');\n    \n    // Parse request body\n    const body = await request.json();\n    const { stockSymbol, investmentAmount, startDate, endDate } = body;\n\n    console.log('📊 Mock analysis request:', {\n      stockSymbol,\n      investmentAmount,\n      startDate,\n      endDate\n    });\n\n    // Validate input\n    if (!stockSymbol || !investmentAmount || !startDate || !endDate) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      );\n    }\n\n    // Mock calculation - simulate TCS investment growth\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const years = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365.25);\n    \n    // Mock TCS performance (approximate historical performance)\n    const mockCAGR = 15.5; // TCS has historically performed well\n    const currentValue = investmentAmount * Math.pow(1 + mockCAGR / 100, years);\n    const absoluteReturn = ((currentValue - investmentAmount) / investmentAmount) * 100;\n\n    // Mock investment result\n    const investmentResult = {\n      scenario: {\n        id: `mock-${Date.now()}`,\n        stockSymbol,\n        investmentAmount: parseFloat(investmentAmount),\n        startDate: new Date(startDate),\n        endDate: new Date(endDate),\n        createdAt: new Date(),\n      },\n      initialValue: investmentAmount,\n      currentValue: Math.round(currentValue),\n      absoluteReturn: Math.round(absoluteReturn * 100) / 100,\n      cagr: mockCAGR,\n      totalReturn: Math.round((currentValue - investmentAmount) * 100) / 100,\n      annualizedReturn: mockCAGR,\n    };\n\n    // Mock benchmark data\n    const comparisonResult = {\n      investment: {\n        name: `${stockSymbol} Investment`,\n        initialValue: investmentAmount,\n        currentValue: Math.round(currentValue),\n        cagr: mockCAGR,\n        absoluteReturn: Math.round(absoluteReturn * 100) / 100,\n      },\n      benchmarks: [\n        {\n          name: 'Gold',\n          initialValue: investmentAmount,\n          currentValue: Math.round(investmentAmount * Math.pow(1.08, years)), // 8% CAGR for gold\n          cagr: 8.0,\n          absoluteReturn: Math.round(((investmentAmount * Math.pow(1.08, years) - investmentAmount) / investmentAmount) * 10000) / 100,\n        },\n        {\n          name: 'Fixed Deposit',\n          initialValue: investmentAmount,\n          currentValue: Math.round(investmentAmount * Math.pow(1.065, years)), // 6.5% CAGR for FD\n          cagr: 6.5,\n          absoluteReturn: Math.round(((investmentAmount * Math.pow(1.065, years) - investmentAmount) / investmentAmount) * 10000) / 100,\n        },\n        {\n          name: 'Nifty 50',\n          initialValue: investmentAmount,\n          currentValue: Math.round(investmentAmount * Math.pow(1.12, years)), // 12% CAGR for Nifty\n          cagr: 12.0,\n          absoluteReturn: Math.round(((investmentAmount * Math.pow(1.12, years) - investmentAmount) / investmentAmount) * 10000) / 100,\n        },\n      ],\n    };\n\n    // Mock comparison summary\n    const comparisonSummary = {\n      investment: {\n        name: `${stockSymbol} Investment`,\n        cagr: mockCAGR,\n        totalReturn: Math.round((currentValue - investmentAmount) * 100) / 100,\n      },\n      benchmarks: comparisonResult.benchmarks,\n      insights: [\n        `Your ${stockSymbol} investment outperformed Fixed Deposits by ${(mockCAGR - 6.5).toFixed(1)}% annually`,\n        `${stockSymbol} delivered ${mockCAGR}% CAGR over the ${years.toFixed(1)} year period`,\n        `Total wealth created: ₹${Math.round((currentValue - investmentAmount)).toLocaleString('en-IN')}`,\n        `Investment multiplied by ${(currentValue / investmentAmount).toFixed(2)}x`,\n      ],\n    };\n\n    // Mock chart data\n    const chartData = {\n      timeSeriesData: [\n        { date: startDate, value: investmentAmount, label: 'Investment Start' },\n        { date: endDate, value: Math.round(currentValue), label: 'Current Value' },\n      ],\n      barChartData: [\n        { name: stockSymbol, value: mockCAGR, color: '#3b82f6' },\n        { name: 'Nifty 50', value: 12.0, color: '#8b5cf6' },\n        { name: 'Gold', value: 8.0, color: '#f59e0b' },\n        { name: 'FD', value: 6.5, color: '#10b981' },\n      ],\n    };\n\n    console.log('✅ Mock analysis completed successfully');\n\n    return NextResponse.json({\n      investmentResult,\n      comparisonResult,\n      comparisonSummary,\n      chartData,\n    });\n\n  } catch (error) {\n    console.error('❌ Mock analysis error:', error);\n    return NextResponse.json(\n      { \n        error: 'Mock analysis failed', \n        details: error instanceof Error ? error.message : 'Unknown error' \n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json({ \n    status: 'OK', \n    message: 'Mock Analysis API is running',\n    note: 'This is a mock implementation for testing purposes'\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,qBAAqB;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAE9D,QAAQ,GAAG,CAAC,6BAA6B;YACvC;YACA;YACA;YACA;QACF;QAEA,iBAAiB;QACjB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,aAAa,CAAC,SAAS;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oDAAoD;QACpD,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,IAAI,KAAK;QACrB,MAAM,QAAQ,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,KAAK,MAAM;QAE/E,4DAA4D;QAC5D,MAAM,WAAW,MAAM,sCAAsC;QAC7D,MAAM,eAAe,mBAAmB,KAAK,GAAG,CAAC,IAAI,WAAW,KAAK;QACrE,MAAM,iBAAiB,AAAC,CAAC,eAAe,gBAAgB,IAAI,mBAAoB;QAEhF,yBAAyB;QACzB,MAAM,mBAAmB;YACvB,UAAU;gBACR,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB;gBACA,kBAAkB,WAAW;gBAC7B,WAAW,IAAI,KAAK;gBACpB,SAAS,IAAI,KAAK;gBAClB,WAAW,IAAI;YACjB;YACA,cAAc;YACd,cAAc,KAAK,KAAK,CAAC;YACzB,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,OAAO;YACnD,MAAM;YACN,aAAa,KAAK,KAAK,CAAC,CAAC,eAAe,gBAAgB,IAAI,OAAO;YACnE,kBAAkB;QACpB;QAEA,sBAAsB;QACtB,MAAM,mBAAmB;YACvB,YAAY;gBACV,MAAM,GAAG,YAAY,WAAW,CAAC;gBACjC,cAAc;gBACd,cAAc,KAAK,KAAK,CAAC;gBACzB,MAAM;gBACN,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,OAAO;YACrD;YACA,YAAY;gBACV;oBACE,MAAM;oBACN,cAAc;oBACd,cAAc,KAAK,KAAK,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM;oBAC3D,MAAM;oBACN,gBAAgB,KAAK,KAAK,CAAC,AAAC,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM,SAAS,gBAAgB,IAAI,mBAAoB,SAAS;gBAC3H;gBACA;oBACE,MAAM;oBACN,cAAc;oBACd,cAAc,KAAK,KAAK,CAAC,mBAAmB,KAAK,GAAG,CAAC,OAAO;oBAC5D,MAAM;oBACN,gBAAgB,KAAK,KAAK,CAAC,AAAC,CAAC,mBAAmB,KAAK,GAAG,CAAC,OAAO,SAAS,gBAAgB,IAAI,mBAAoB,SAAS;gBAC5H;gBACA;oBACE,MAAM;oBACN,cAAc;oBACd,cAAc,KAAK,KAAK,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM;oBAC3D,MAAM;oBACN,gBAAgB,KAAK,KAAK,CAAC,AAAC,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM,SAAS,gBAAgB,IAAI,mBAAoB,SAAS;gBAC3H;aACD;QACH;QAEA,0BAA0B;QAC1B,MAAM,oBAAoB;YACxB,YAAY;gBACV,MAAM,GAAG,YAAY,WAAW,CAAC;gBACjC,MAAM;gBACN,aAAa,KAAK,KAAK,CAAC,CAAC,eAAe,gBAAgB,IAAI,OAAO;YACrE;YACA,YAAY,iBAAiB,UAAU;YACvC,UAAU;gBACR,CAAC,KAAK,EAAE,YAAY,2CAA2C,EAAE,CAAC,WAAW,GAAG,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC;gBACxG,GAAG,YAAY,WAAW,EAAE,SAAS,gBAAgB,EAAE,MAAM,OAAO,CAAC,GAAG,YAAY,CAAC;gBACrF,CAAC,uBAAuB,EAAE,KAAK,KAAK,CAAE,eAAe,kBAAmB,cAAc,CAAC,UAAU;gBACjG,CAAC,yBAAyB,EAAE,CAAC,eAAe,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;aAC5E;QACH;QAEA,kBAAkB;QAClB,MAAM,YAAY;YAChB,gBAAgB;gBACd;oBAAE,MAAM;oBAAW,OAAO;oBAAkB,OAAO;gBAAmB;gBACtE;oBAAE,MAAM;oBAAS,OAAO,KAAK,KAAK,CAAC;oBAAe,OAAO;gBAAgB;aAC1E;YACD,cAAc;gBACZ;oBAAE,MAAM;oBAAa,OAAO;oBAAU,OAAO;gBAAU;gBACvD;oBAAE,MAAM;oBAAY,OAAO;oBAAM,OAAO;gBAAU;gBAClD;oBAAE,MAAM;oBAAQ,OAAO;oBAAK,OAAO;gBAAU;gBAC7C;oBAAE,MAAM;oBAAM,OAAO;oBAAK,OAAO;gBAAU;aAC5C;QACH;QAEA,QAAQ,GAAG,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,QAAQ;QACR,SAAS;QACT,MAAM;IACR;AACF", "debugId": null}}]}