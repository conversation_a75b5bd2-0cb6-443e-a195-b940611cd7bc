{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/config/index.ts"], "sourcesContent": ["// Configuration for the What If investment analysis tool\n\nexport const APP_CONFIG = {\n  name: 'What If',\n  description: 'Investment Analysis Tool for Indian Equities',\n  version: '1.0.0',\n  author: 'What If Team',\n} as const;\n\nexport const API_CONFIG = {\n  angelOne: {\n    baseUrl: 'https://apiconnect.angelone.in',\n    version: 'v1',\n    endpoints: {\n      login: '/rest/auth/angelbroking/user/v1/loginByPassword',\n      profile: '/rest/secure/angelbroking/user/v1/getProfile',\n      historicalData: '/rest/secure/angelbroking/historical/v1/getCandleData',\n      ltp: '/rest/secure/angelbroking/order/v1/getLtpData',\n      logout: '/rest/secure/angelbroking/user/v1/logout',\n    },\n  },\n  rateLimit: {\n    requestsPerSecond: 10,\n    requestsPerMinute: 100,\n  },\n} as const;\n\nexport const MARKET_CONFIG = {\n  exchanges: ['NSE', 'BSE'] as const,\n  segments: ['EQUITY'] as const, // Excluding OPTIONS as per requirement\n  tradingHours: {\n    start: '09:15',\n    end: '15:30',\n    timezone: 'Asia/Kolkata',\n  },\n  holidays: [], // To be populated with market holidays\n} as const;\n\nexport const BENCHMARK_CONFIG = {\n  types: {\n    GOLD: {\n      name: 'Gold',\n      symbol: 'GOLD',\n      description: 'Gold prices in INR per 10 grams',\n    },\n    FD: {\n      name: 'Fixed Deposit',\n      symbol: 'FD',\n      description: 'Average FD rates from major banks',\n      defaultRate: 6.5, // Default FD rate percentage\n    },\n    NIFTY: {\n      name: 'Nifty 50',\n      symbol: 'NIFTY',\n      description: 'NSE Nifty 50 Index',\n      token: '********', // Angel One token for Nifty 50\n    },\n  },\n} as const;\n\nexport const CALCULATION_CONFIG = {\n  precision: {\n    currency: 2,\n    percentage: 2,\n    cagr: 2,\n  },\n  defaults: {\n    fdRate: 6.5, // Default FD rate percentage\n    inflationRate: 6.0, // Default inflation rate\n  },\n} as const;\n\nexport const UI_CONFIG = {\n  theme: {\n    primary: '#1f2937',\n    secondary: '#374151',\n    accent: '#3b82f6',\n    success: '#10b981',\n    warning: '#f59e0b',\n    error: '#ef4444',\n  },\n  charts: {\n    defaultHeight: 400,\n    colors: {\n      investment: '#3b82f6',\n      gold: '#f59e0b',\n      fd: '#10b981',\n      nifty: '#8b5cf6',\n    },\n  },\n} as const;\n\nexport const STORAGE_CONFIG = {\n  keys: {\n    userPreferences: 'whatif_user_preferences',\n    savedScenarios: 'whatif_saved_scenarios',\n    apiCredentials: 'whatif_api_credentials',\n  },\n  encryption: {\n    enabled: true,\n    algorithm: 'AES-256-GCM',\n  },\n} as const;\n\n// Environment-specific configuration\nexport const getEnvironmentConfig = () => {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const isProduction = process.env.NODE_ENV === 'production';\n\n  return {\n    isDevelopment,\n    isProduction,\n    apiUrl: process.env.NEXT_PUBLIC_API_URL || API_CONFIG.angelOne.baseUrl,\n    enableLogging: isDevelopment,\n    enableAnalytics: isProduction,\n  };\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;;;;;;AAElD,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;AACV;AAEO,MAAM,aAAa;IACxB,UAAU;QACR,SAAS;QACT,SAAS;QACT,WAAW;YACT,OAAO;YACP,SAAS;YACT,gBAAgB;YAChB,KAAK;YACL,QAAQ;QACV;IACF;IACA,WAAW;QACT,mBAAmB;QACnB,mBAAmB;IACrB;AACF;AAEO,MAAM,gBAAgB;IAC3B,WAAW;QAAC;QAAO;KAAM;IACzB,UAAU;QAAC;KAAS;IACpB,cAAc;QACZ,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IACA,UAAU,EAAE;AACd;AAEO,MAAM,mBAAmB;IAC9B,OAAO;QACL,MAAM;YAC<PERSON>,MAAM;YACN,QAAQ;YACR,aAAa;QACf;QACA,IAAI;YACF,MAAM;YACN,QAAQ;YACR,aAAa;YACb,aAAa;QACf;QACA,OAAO;YACL,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;IACF;AACF;AAEO,MAAM,qBAAqB;IAChC,WAAW;QACT,UAAU;QACV,YAAY;QACZ,MAAM;IACR;IACA,UAAU;QACR,QAAQ;QACR,eAAe;IACjB;AACF;AAEO,MAAM,YAAY;IACvB,OAAO;QACL,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,SAAS;QACT,OAAO;IACT;IACA,QAAQ;QACN,eAAe;QACf,QAAQ;YACN,YAAY;YACZ,MAAM;YACN,IAAI;YACJ,OAAO;QACT;IACF;AACF;AAEO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;IAClB;IACA,YAAY;QACV,SAAS;QACT,WAAW;IACb;AACF;AAGO,MAAM,uBAAuB;IAClC,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,eAAe,oDAAyB;IAE9C,OAAO;QACL;QACA;QACA,QAAQ,QAAQ,GAAG,CAAC,mBAAmB,IAAI,WAAW,QAAQ,CAAC,OAAO;QACtE,eAAe;QACf,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/security/index.ts"], "sourcesContent": ["// Security utilities for the What If investment analysis tool\n\nimport { STORAGE_CONFIG } from '../config';\n\n/**\n * Validate environment variables\n * @returns Validation result with missing variables\n */\nexport function validateEnvironment(): {\n  isValid: boolean;\n  missingVars: string[];\n  warnings: string[];\n} {\n  const requiredVars = [\n    'NEXT_PUBLIC_ANGEL_ONE_API_URL',\n    'NEXT_PUBLIC_APP_NAME',\n    'NEXT_PUBLIC_APP_VERSION',\n  ];\n\n  const optionalVars = [\n    'ANGEL_ONE_API_KEY',\n    'ANGEL_ONE_CLIENT_ID',\n    'ANGEL_ONE_PASSWORD',\n    'ANGEL_ONE_TOTP_SECRET',\n  ];\n\n  const missingVars: string[] = [];\n  const warnings: string[] = [];\n\n  // Check required variables\n  requiredVars.forEach(varName => {\n    if (!process.env[varName]) {\n      missingVars.push(varName);\n    }\n  });\n\n  // Check optional but important variables\n  optionalVars.forEach(varName => {\n    if (!process.env[varName]) {\n      warnings.push(`${varName} is not set - API functionality will be limited`);\n    }\n  });\n\n  // Check for development secrets in production\n  if (process.env.NODE_ENV === 'production') {\n    if (process.env.NEXTAUTH_SECRET === 'development_secret_change_in_production') {\n      missingVars.push('NEXTAUTH_SECRET (using development value in production)');\n    }\n  }\n\n  return {\n    isValid: missingVars.length === 0,\n    missingVars,\n    warnings,\n  };\n}\n\n/**\n * Sanitize sensitive data for logging\n * @param data - Data object to sanitize\n * @param visited - Set to track visited objects (prevents circular references)\n * @returns Sanitized data object\n */\nexport function sanitizeForLogging(\n  data: Record<string, unknown>,\n  visited: WeakSet<object> = new WeakSet()\n): Record<string, unknown> {\n  const sensitiveKeys = [\n    'password',\n    'token',\n    'secret',\n    'key',\n    'auth',\n    'credential',\n    'totp',\n  ];\n\n  // Handle non-object types\n  if (typeof data !== 'object' || data === null) {\n    return { value: data };\n  }\n\n  // Handle circular references\n  if (visited.has(data)) {\n    return { '[Circular Reference]': true };\n  }\n  visited.add(data);\n\n  const sanitized: Record<string, unknown> = {};\n\n  try {\n    Object.keys(data).forEach(key => {\n      const lowerKey = key.toLowerCase();\n      const isSensitive = sensitiveKeys.some(sensitiveKey =>\n        lowerKey.includes(sensitiveKey)\n      );\n\n      if (isSensitive) {\n        sanitized[key] = '[REDACTED]';\n      } else if (typeof data[key] === 'object' && data[key] !== null) {\n        // Handle arrays\n        if (Array.isArray(data[key])) {\n          sanitized[key] = '[Array]';\n        } else {\n          sanitized[key] = sanitizeForLogging(data[key] as Record<string, unknown>, visited);\n        }\n      } else {\n        sanitized[key] = data[key];\n      }\n    });\n  } catch (error) {\n    return { '[Sanitization Error]': 'Unable to sanitize object' };\n  }\n\n  return sanitized;\n}\n\n/**\n * Simple encryption for local storage (basic obfuscation)\n * Note: This is not cryptographically secure, just basic obfuscation\n * @param text - Text to encrypt\n * @returns Encrypted text\n */\nexport function simpleEncrypt(text: string): string {\n  if (!STORAGE_CONFIG.encryption.enabled) {\n    return text;\n  }\n\n  // Simple base64 encoding with character shifting (not secure, just obfuscation)\n  const shifted = text\n    .split('')\n    .map(char => String.fromCharCode(char.charCodeAt(0) + 3))\n    .join('');\n  \n  return btoa(shifted);\n}\n\n/**\n * Simple decryption for local storage\n * @param encryptedText - Encrypted text to decrypt\n * @returns Decrypted text\n */\nexport function simpleDecrypt(encryptedText: string): string {\n  if (!STORAGE_CONFIG.encryption.enabled) {\n    return encryptedText;\n  }\n\n  try {\n    const decoded = atob(encryptedText);\n    return decoded\n      .split('')\n      .map(char => String.fromCharCode(char.charCodeAt(0) - 3))\n      .join('');\n  } catch {\n    return encryptedText; // Return as-is if decryption fails\n  }\n}\n\n/**\n * Validate API key format\n * @param apiKey - API key to validate\n * @returns Validation result\n */\nexport function validateApiKey(apiKey: string): {\n  isValid: boolean;\n  error?: string;\n} {\n  if (!apiKey || typeof apiKey !== 'string') {\n    return { isValid: false, error: 'API key is required' };\n  }\n\n  if (apiKey.length < 10) {\n    return { isValid: false, error: 'API key is too short' };\n  }\n\n  if (apiKey.includes(' ')) {\n    return { isValid: false, error: 'API key should not contain spaces' };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Rate limiting utility\n */\nexport class RateLimiter {\n  private requests: number[] = [];\n  private readonly maxRequests: number;\n  private readonly timeWindow: number; // in milliseconds\n\n  constructor(maxRequests: number, timeWindowSeconds: number) {\n    this.maxRequests = maxRequests;\n    this.timeWindow = timeWindowSeconds * 1000;\n  }\n\n  /**\n   * Check if request is allowed\n   * @returns Whether request is allowed\n   */\n  isAllowed(): boolean {\n    const now = Date.now();\n    \n    // Remove old requests outside the time window\n    this.requests = this.requests.filter(time => now - time < this.timeWindow);\n    \n    // Check if we're under the limit\n    if (this.requests.length < this.maxRequests) {\n      this.requests.push(now);\n      return true;\n    }\n    \n    return false;\n  }\n\n  /**\n   * Get time until next request is allowed\n   * @returns Milliseconds until next request\n   */\n  getTimeUntilReset(): number {\n    if (this.requests.length === 0) return 0;\n    \n    const oldestRequest = Math.min(...this.requests);\n    const timeUntilReset = this.timeWindow - (Date.now() - oldestRequest);\n    \n    return Math.max(0, timeUntilReset);\n  }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;;;;;;AAE9D;;AAMO,SAAS;IAKd,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,MAAM,cAAwB,EAAE;IAChC,MAAM,WAAqB,EAAE;IAE7B,2BAA2B;IAC3B,aAAa,OAAO,CAAC,CAAA;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE;YACzB,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,yCAAyC;IACzC,aAAa,OAAO,CAAC,CAAA;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE;YACzB,SAAS,IAAI,CAAC,GAAG,QAAQ,+CAA+C,CAAC;QAC3E;IACF;IAEA,8CAA8C;IAC9C,uCAA2C;;IAI3C;IAEA,OAAO;QACL,SAAS,YAAY,MAAM,KAAK;QAChC;QACA;IACF;AACF;AAQO,SAAS,mBACd,IAA6B,EAC7B,UAA2B,IAAI,SAAS;IAExC,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,0BAA0B;IAC1B,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM;QAC7C,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,6BAA6B;IAC7B,IAAI,QAAQ,GAAG,CAAC,OAAO;QACrB,OAAO;YAAE,wBAAwB;QAAK;IACxC;IACA,QAAQ,GAAG,CAAC;IAEZ,MAAM,YAAqC,CAAC;IAE5C,IAAI;QACF,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;YACxB,MAAM,WAAW,IAAI,WAAW;YAChC,MAAM,cAAc,cAAc,IAAI,CAAC,CAAA,eACrC,SAAS,QAAQ,CAAC;YAGpB,IAAI,aAAa;gBACf,SAAS,CAAC,IAAI,GAAG;YACnB,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,IAAI,KAAK,MAAM;gBAC9D,gBAAgB;gBAChB,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG;oBAC5B,SAAS,CAAC,IAAI,GAAG;gBACnB,OAAO;oBACL,SAAS,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI,EAA6B;gBAC5E;YACF,OAAO;gBACL,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,wBAAwB;QAA4B;IAC/D;IAEA,OAAO;AACT;AAQO,SAAS,cAAc,IAAY;IACxC,IAAI,CAAC,+HAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,OAAO,EAAE;QACtC,OAAO;IACT;IAEA,gFAAgF;IAChF,MAAM,UAAU,KACb,KAAK,CAAC,IACN,GAAG,CAAC,CAAA,OAAQ,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK,IACrD,IAAI,CAAC;IAER,OAAO,KAAK;AACd;AAOO,SAAS,cAAc,aAAqB;IACjD,IAAI,CAAC,+HAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,OAAO,EAAE;QACtC,OAAO;IACT;IAEA,IAAI;QACF,MAAM,UAAU,KAAK;QACrB,OAAO,QACJ,KAAK,CAAC,IACN,GAAG,CAAC,CAAA,OAAQ,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK,IACrD,IAAI,CAAC;IACV,EAAE,OAAM;QACN,OAAO,eAAe,mCAAmC;IAC3D;AACF;AAOO,SAAS,eAAe,MAAc;IAI3C,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;IAEA,IAAI,OAAO,MAAM,GAAG,IAAI;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuB;IACzD;IAEA,IAAI,OAAO,QAAQ,CAAC,MAAM;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM;IACH,WAAqB,EAAE,CAAC;IACf,YAAoB;IACpB,WAAmB;IAEpC,YAAY,WAAmB,EAAE,iBAAyB,CAAE;QAC1D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,oBAAoB;IACxC;IAEA;;;GAGC,GACD,YAAqB;QACnB,MAAM,MAAM,KAAK,GAAG;QAEpB,8CAA8C;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,OAAQ,MAAM,OAAO,IAAI,CAAC,UAAU;QAEzE,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YAC3C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACnB,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,oBAA4B;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,OAAO;QAEvC,MAAM,gBAAgB,KAAK,GAAG,IAAI,IAAI,CAAC,QAAQ;QAC/C,MAAM,iBAAiB,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,GAAG,KAAK,aAAa;QAEpE,OAAO,KAAK,GAAG,CAAC,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/api/angelone.ts"], "sourcesContent": ["// Angel One API client for the What If investment analysis tool\n\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { authenticator } from 'otplib';\nimport { API_CONFIG } from '../config';\nimport { RateLimiter, sanitizeForLogging } from '../security';\nimport {\n  AngelOneLoginRequest,\n  AngelOneLoginResponse,\n  AngelOneHistoricalRequest,\n  AngelOneHistoricalResponse,\n  AngelOneLTPRequest,\n  AngelOneLTPResponse,\n  HistoricalPrice,\n  FullHistoricalPrice,\n  StockData,\n} from '../types';\n\nexport class AngelOneClient {\n  private axiosInstance: AxiosInstance;\n  private jwtToken: string | null = null;\n  private refreshToken: string | null = null;\n  private feedToken: string | null = null;\n  private rateLimiter: RateLimiter;\n  private readonly apiKey: string;\n  private readonly clientId: string;\n  private readonly password: string;\n  private readonly totpSecret: string;\n\n  constructor(config: {\n    apiKey: string;\n    clientId: string;\n    password: string;\n    totpSecret: string;\n  }) {\n    this.apiKey = config.apiKey;\n    this.clientId = config.clientId;\n    this.password = config.password;\n    this.totpSecret = config.totpSecret;\n\n    // Initialize rate limiter\n    this.rateLimiter = new RateLimiter(\n      API_CONFIG.rateLimit.requestsPerSecond,\n      1\n    );\n\n    // Create axios instance\n    this.axiosInstance = axios.create({\n      baseURL: API_CONFIG.angelOne.baseUrl,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'X-UserType': 'USER',\n        'X-SourceID': 'WEB',\n        'X-ClientLocalIP': '127.0.0.1',\n        'X-ClientPublicIP': '127.0.0.1',\n        'X-MACAddress': '00:00:00:00:00:00',\n        'X-PrivateKey': this.apiKey,\n      },\n    });\n\n    // Add request interceptor for rate limiting\n    this.axiosInstance.interceptors.request.use(\n      async (config) => {\n        // Wait if rate limit exceeded\n        while (!this.rateLimiter.isAllowed()) {\n          const waitTime = this.rateLimiter.getTimeUntilReset();\n          await new Promise(resolve => setTimeout(resolve, waitTime));\n        }\n\n        // Add JWT token if available\n        if (this.jwtToken) {\n          config.headers.Authorization = `Bearer ${this.jwtToken}`;\n        }\n\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Add response interceptor for error handling\n    this.axiosInstance.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        if (error.response?.status === 401 && this.refreshToken) {\n          // Try to refresh token\n          try {\n            await this.refreshAuthToken();\n            // Retry the original request\n            return this.axiosInstance.request(error.config);\n          } catch {\n            // Refresh failed, need to re-login\n            this.clearTokens();\n            throw new Error('Authentication failed. Please login again.');\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * Generate TOTP for authentication\n   */\n  private generateTOTP(): string {\n    return authenticator.generate(this.totpSecret);\n  }\n\n  /**\n   * Clear stored tokens\n   */\n  private clearTokens(): void {\n    this.jwtToken = null;\n    this.refreshToken = null;\n    this.feedToken = null;\n  }\n\n  /**\n   * Login to Angel One API\n   */\n  async login(): Promise<{ success: boolean; message: string }> {\n    try {\n      const totp = this.generateTOTP();\n      \n      const loginRequest: AngelOneLoginRequest = {\n        clientcode: this.clientId,\n        password: this.password,\n        totp: totp,\n      };\n\n      console.log('Attempting login with:', sanitizeForLogging(loginRequest as unknown as Record<string, unknown>));\n\n      const response: AxiosResponse<AngelOneLoginResponse> = await this.axiosInstance.post(\n        API_CONFIG.angelOne.endpoints.login,\n        loginRequest\n      );\n\n      if (response.data.status && response.data.data) {\n        this.jwtToken = response.data.data.jwtToken;\n        this.refreshToken = response.data.data.refreshToken;\n        this.feedToken = response.data.data.feedToken;\n\n        return {\n          success: true,\n          message: 'Login successful',\n        };\n      } else {\n        return {\n          success: false,\n          message: response.data.message || 'Login failed',\n        };\n      }\n    } catch (error) {\n      console.error('Login error:', sanitizeForLogging({ error } as Record<string, unknown>));\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Login failed',\n      };\n    }\n  }\n\n  /**\n   * Refresh authentication token\n   */\n  private async refreshAuthToken(): Promise<void> {\n    if (!this.refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    const response = await this.axiosInstance.post('/rest/auth/angelbroking/jwt/v1/generateTokens', {\n      refreshToken: this.refreshToken,\n    });\n\n    if (response.data.status && response.data.data) {\n      this.jwtToken = response.data.data.jwtToken;\n      this.refreshToken = response.data.data.refreshToken;\n    } else {\n      throw new Error('Token refresh failed');\n    }\n  }\n\n  /**\n   * Check if client is authenticated\n   */\n  isAuthenticated(): boolean {\n    return this.jwtToken !== null;\n  }\n\n  /**\n   * Get historical data for a stock (optimized for closing prices only)\n   */\n  async getHistoricalData(request: AngelOneHistoricalRequest): Promise<HistoricalPrice[]> {\n    if (!this.isAuthenticated()) {\n      throw new Error('Not authenticated. Please login first.');\n    }\n\n    try {\n      const response: AxiosResponse<AngelOneHistoricalResponse> = await this.axiosInstance.post(\n        API_CONFIG.angelOne.endpoints.historicalData,\n        request\n      );\n\n      if (response.data.status && response.data.data) {\n        // Optimized: Only extract date and closing price for investment calculations\n        return response.data.data.map(item => ({\n          date: new Date(item[0]),\n          close: item[4], // Only closing price needed\n        }));\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch historical data');\n      }\n    } catch (error) {\n      console.error('Historical data error:', sanitizeForLogging({ error, request } as Record<string, unknown>));\n      throw error;\n    }\n  }\n\n  /**\n   * Get full OHLC historical data (if needed for advanced analysis)\n   */\n  async getFullHistoricalData(request: AngelOneHistoricalRequest): Promise<FullHistoricalPrice[]> {\n    if (!this.isAuthenticated()) {\n      throw new Error('Not authenticated. Please login first.');\n    }\n\n    try {\n      const response: AxiosResponse<AngelOneHistoricalResponse> = await this.axiosInstance.post(\n        API_CONFIG.angelOne.endpoints.historicalData,\n        request\n      );\n\n      if (response.data.status && response.data.data) {\n        return response.data.data.map(item => ({\n          date: new Date(item[0]),\n          open: item[1],\n          high: item[2],\n          low: item[3],\n          close: item[4],\n          volume: item[5],\n        }));\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch historical data');\n      }\n    } catch (error) {\n      console.error('Full historical data error:', sanitizeForLogging({ error, request } as Record<string, unknown>));\n      throw error;\n    }\n  }\n\n  /**\n   * Get current price (LTP) for a stock\n   */\n  async getCurrentPrice(request: AngelOneLTPRequest): Promise<StockData> {\n    if (!this.isAuthenticated()) {\n      throw new Error('Not authenticated. Please login first.');\n    }\n\n    try {\n      const response: AxiosResponse<AngelOneLTPResponse> = await this.axiosInstance.post(\n        API_CONFIG.angelOne.endpoints.ltp,\n        request\n      );\n\n      if (response.data.status && response.data.data) {\n        const data = response.data.data;\n        return {\n          symbol: data.tradingsymbol,\n          name: data.tradingsymbol, // Angel One doesn't provide company name in LTP response\n          exchange: data.exchange as 'NSE' | 'BSE',\n          token: data.symboltoken,\n          currentPrice: data.ltp,\n          lastUpdated: new Date(),\n        };\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch current price');\n      }\n    } catch (error) {\n      console.error('Current price error:', sanitizeForLogging({ error, request } as Record<string, unknown>));\n      throw error;\n    }\n  }\n\n  /**\n   * Logout from Angel One API\n   */\n  async logout(): Promise<{ success: boolean; message: string }> {\n    if (!this.isAuthenticated()) {\n      return { success: true, message: 'Already logged out' };\n    }\n\n    try {\n      await this.axiosInstance.post(API_CONFIG.angelOne.endpoints.logout, {\n        clientcode: this.clientId,\n      });\n\n      this.clearTokens();\n      \n      return {\n        success: true,\n        message: 'Logout successful',\n      };\n    } catch (error) {\n      console.error('Logout error:', sanitizeForLogging({ error } as Record<string, unknown>));\n      this.clearTokens(); // Clear tokens anyway\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Logout failed',\n      };\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,gEAAgE;;;;AAEhE;AACA;AACA;AACA;;;;;AAaO,MAAM;IACH,cAA6B;IAC7B,WAA0B,KAAK;IAC/B,eAA8B,KAAK;IACnC,YAA2B,KAAK;IAChC,YAAyB;IAChB,OAAe;IACf,SAAiB;IACjB,SAAiB;IACjB,WAAmB;IAEpC,YAAY,MAKX,CAAE;QACD,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,QAAQ;QAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,QAAQ;QAC/B,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU;QAEnC,0BAA0B;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,iIAAA,CAAA,cAAW,CAChC,+HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,iBAAiB,EACtC;QAGF,wBAAwB;QACxB,IAAI,CAAC,aAAa,GAAG,uIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAChC,SAAS,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,OAAO;YACpC,SAAS;YACT,SAAS;gBACP,gBAAgB;gBAChB,UAAU;gBACV,cAAc;gBACd,cAAc;gBACd,mBAAmB;gBACnB,oBAAoB;gBACpB,gBAAgB;gBAChB,gBAAgB,IAAI,CAAC,MAAM;YAC7B;QACF;QAEA,4CAA4C;QAC5C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACzC,OAAO;YACL,8BAA8B;YAC9B,MAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,GAAI;gBACpC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBACnD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,6BAA6B;YAC7B,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;YAC1D;YAEA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,8CAA8C;QAC9C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC1C,CAAC,WAAa,UACd,OAAO;YACL,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,IAAI,CAAC,YAAY,EAAE;gBACvD,uBAAuB;gBACvB,IAAI;oBACF,MAAM,IAAI,CAAC,gBAAgB;oBAC3B,6BAA6B;oBAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,MAAM;gBAChD,EAAE,OAAM;oBACN,mCAAmC;oBACnC,IAAI,CAAC,WAAW;oBAChB,MAAM,IAAI,MAAM;gBAClB;YACF;YACA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA;;GAEC,GACD,AAAQ,eAAuB;QAC7B,OAAO,iIAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU;IAC/C;IAEA;;GAEC,GACD,AAAQ,cAAoB;QAC1B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA;;GAEC,GACD,MAAM,QAAwD;QAC5D,IAAI;YACF,MAAM,OAAO,IAAI,CAAC,YAAY;YAE9B,MAAM,eAAqC;gBACzC,YAAY,IAAI,CAAC,QAAQ;gBACzB,UAAU,IAAI,CAAC,QAAQ;gBACvB,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,0BAA0B,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;YAEzD,MAAM,WAAiD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAClF,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,EACnC;YAGF,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAC3C,IAAI,CAAC,YAAY,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;gBACnD,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;gBAE7C,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;YAAM;YACzD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAc,mBAAkC;QAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iDAAiD;YAC9F,cAAc,IAAI,CAAC,YAAY;QACjC;QAEA,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YAC9C,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YAC3C,IAAI,CAAC,YAAY,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;QACrD,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,kBAA2B;QACzB,OAAO,IAAI,CAAC,QAAQ,KAAK;IAC3B;IAEA;;GAEC,GACD,MAAM,kBAAkB,OAAkC,EAA8B;QACtF,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAsD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CACvF,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,EAC5C;YAGF,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,6EAA6E;gBAC7E,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACrC,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE;wBACtB,OAAO,IAAI,CAAC,EAAE;oBAChB,CAAC;YACH,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;gBAAO;YAAQ;YAC5E,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,OAAkC,EAAkC;QAC9F,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAsD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CACvF,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,EAC5C;YAGF,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACrC,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE;wBACtB,MAAM,IAAI,CAAC,EAAE;wBACb,MAAM,IAAI,CAAC,EAAE;wBACb,KAAK,IAAI,CAAC,EAAE;wBACZ,OAAO,IAAI,CAAC,EAAE;wBACd,QAAQ,IAAI,CAAC,EAAE;oBACjB,CAAC;YACH,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;gBAAO;YAAQ;YACjF,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAA2B,EAAsB;QACrE,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAA+C,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAChF,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EACjC;YAGF,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC/B,OAAO;oBACL,QAAQ,KAAK,aAAa;oBAC1B,MAAM,KAAK,aAAa;oBACxB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,WAAW;oBACvB,cAAc,KAAK,GAAG;oBACtB,aAAa,IAAI;gBACnB;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;gBAAO;YAAQ;YAC1E,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,SAAyD;QAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YAC3B,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAqB;QACxD;QAEA,IAAI;YACF,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,+HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;gBAClE,YAAY,IAAI,CAAC,QAAQ;YAC3B;YAEA,IAAI,CAAC,WAAW;YAEhB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;YAAM;YAC1D,IAAI,CAAC,WAAW,IAAI,sBAAsB;YAC1C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/utils/index.ts"], "sourcesContent": ["// Utility functions for the What If investment analysis tool\n\nimport { CALCULATION_CONFIG } from '../config';\n\n/**\n * Calculate Compound Annual Growth Rate (CAGR)\n * @param initialValue - Initial investment value\n * @param finalValue - Final investment value\n * @param years - Number of years\n * @returns CAGR as a percentage\n */\nexport function calculateCAGR(\n  initialValue: number,\n  finalValue: number,\n  years: number\n): number {\n  if (initialValue <= 0 || finalValue <= 0 || years <= 0) {\n    return 0;\n  }\n  \n  const cagr = (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100;\n  return Number(cagr.toFixed(CALCULATION_CONFIG.precision.cagr));\n}\n\n/**\n * Calculate absolute return\n * @param initialValue - Initial investment value\n * @param finalValue - Final investment value\n * @returns Absolute return as a percentage\n */\nexport function calculateAbsoluteReturn(\n  initialValue: number,\n  finalValue: number\n): number {\n  if (initialValue <= 0) return 0;\n  \n  const absoluteReturn = ((finalValue - initialValue) / initialValue) * 100;\n  return Number(absoluteReturn.toFixed(CALCULATION_CONFIG.precision.percentage));\n}\n\n/**\n * Calculate the number of years between two dates\n * @param startDate - Start date\n * @param endDate - End date\n * @returns Number of years (with decimals)\n */\nexport function calculateYearsBetweenDates(\n  startDate: Date,\n  endDate: Date\n): number {\n  const timeDiff = endDate.getTime() - startDate.getTime();\n  const daysDiff = timeDiff / (1000 * 3600 * 24);\n  return daysDiff / 365.25; // Account for leap years\n}\n\n/**\n * Format currency value\n * @param value - Numeric value\n * @param currency - Currency code (default: INR)\n * @returns Formatted currency string\n */\nexport function formatCurrency(value: number, currency: string = 'INR'): string {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: CALCULATION_CONFIG.precision.currency,\n    maximumFractionDigits: CALCULATION_CONFIG.precision.currency,\n  }).format(value);\n}\n\n/**\n * Format percentage value\n * @param value - Numeric value\n * @returns Formatted percentage string\n */\nexport function formatPercentage(value: number): string {\n  return `${value.toFixed(CALCULATION_CONFIG.precision.percentage)}%`;\n}\n\n/**\n * Format large numbers with Indian numbering system\n * @param value - Numeric value\n * @returns Formatted number string (e.g., 1,00,000)\n */\nexport function formatIndianNumber(value: number): string {\n  return new Intl.NumberFormat('en-IN').format(value);\n}\n\n/**\n * Validate date range\n * @param startDate - Start date\n * @param endDate - End date\n * @returns Validation result\n */\nexport function validateDateRange(startDate: Date, endDate: Date): {\n  isValid: boolean;\n  error?: string;\n} {\n  const now = new Date();\n\n  if (startDate >= endDate) {\n    return { isValid: false, error: 'Start date must be before end date' };\n  }\n\n  if (startDate > now) {\n    return { isValid: false, error: 'Start date cannot be in the future' };\n  }\n\n  if (endDate > now) {\n    return { isValid: false, error: 'End date cannot be in the future' };\n  }\n\n  // Angel One API historical data limits (based on testing)\n  const angelOneMinDate = new Date('2018-01-01'); // Conservative estimate - Angel One reliable data from 2018\n  if (startDate < angelOneMinDate) {\n    return {\n      isValid: false,\n      error: `Start date cannot be earlier than ${angelOneMinDate.toLocaleDateString()}. Angel One API historical data is limited to recent years.`\n    };\n  }\n\n  // Maximum date range validation (Angel One may have limits on large ranges)\n  const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));\n  const maxDays = 3650; // ~10 years maximum\n\n  if (daysDifference > maxDays) {\n    return {\n      isValid: false,\n      error: `Date range too large (${daysDifference} days). Maximum allowed is ${maxDays} days (~10 years).`\n    };\n  }\n\n  // Minimum date range validation\n  const minDays = 30; // At least 1 month for meaningful analysis\n  if (daysDifference < minDays) {\n    return {\n      isValid: false,\n      error: `Date range too small (${daysDifference} days). Minimum required is ${minDays} days for meaningful analysis.`\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Generate unique ID\n * @returns Unique string ID\n */\nexport function generateId(): string {\n  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Debounce function\n * @param func - Function to debounce\n * @param wait - Wait time in milliseconds\n * @returns Debounced function\n */\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Check if market is open\n * @returns Boolean indicating if market is currently open\n */\nexport function isMarketOpen(): boolean {\n  const now = new Date();\n  const currentTime = now.toLocaleTimeString('en-IN', {\n    timeZone: 'Asia/Kolkata',\n    hour12: false,\n  });\n  \n  const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n  const isWithinTradingHours = currentTime >= '09:15:00' && currentTime <= '15:30:00';\n  \n  return isWeekday && isWithinTradingHours;\n}\n\n/**\n * Safe JSON parse\n * @param jsonString - JSON string to parse\n * @param defaultValue - Default value if parsing fails\n * @returns Parsed object or default value\n */\nexport function safeJsonParse<T>(jsonString: string, defaultValue: T): T {\n  try {\n    return JSON.parse(jsonString);\n  } catch {\n    return defaultValue;\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;AAE7D;;AASO,SAAS,cACd,YAAoB,EACpB,UAAkB,EAClB,KAAa;IAEb,IAAI,gBAAgB,KAAK,cAAc,KAAK,SAAS,GAAG;QACtD,OAAO;IACT;IAEA,MAAM,OAAO,CAAC,KAAK,GAAG,CAAC,aAAa,cAAc,IAAI,SAAS,CAAC,IAAI;IACpE,OAAO,OAAO,KAAK,OAAO,CAAC,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,IAAI;AAC9D;AAQO,SAAS,wBACd,YAAoB,EACpB,UAAkB;IAElB,IAAI,gBAAgB,GAAG,OAAO;IAE9B,MAAM,iBAAiB,AAAC,CAAC,aAAa,YAAY,IAAI,eAAgB;IACtE,OAAO,OAAO,eAAe,OAAO,CAAC,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,UAAU;AAC9E;AAQO,SAAS,2BACd,SAAe,EACf,OAAa;IAEb,MAAM,WAAW,QAAQ,OAAO,KAAK,UAAU,OAAO;IACtD,MAAM,WAAW,WAAW,CAAC,OAAO,OAAO,EAAE;IAC7C,OAAO,WAAW,QAAQ,yBAAyB;AACrD;AAQO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,QAAQ;QAC5D,uBAAuB,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,QAAQ;IAC9D,GAAG,MAAM,CAAC;AACZ;AAOO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,MAAM,OAAO,CAAC,+HAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;AACrE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAQO,SAAS,kBAAkB,SAAe,EAAE,OAAa;IAI9D,MAAM,MAAM,IAAI;IAEhB,IAAI,aAAa,SAAS;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;IAEA,IAAI,YAAY,KAAK;QACnB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;IAEA,IAAI,UAAU,KAAK;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmC;IACrE;IAEA,0DAA0D;IAC1D,MAAM,kBAAkB,IAAI,KAAK,eAAe,4DAA4D;IAC5G,IAAI,YAAY,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,kCAAkC,EAAE,gBAAgB,kBAAkB,GAAG,2DAA2D,CAAC;QAC/I;IACF;IAEA,4EAA4E;IAC5E,MAAM,iBAAiB,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IACjG,MAAM,UAAU,MAAM,oBAAoB;IAE1C,IAAI,iBAAiB,SAAS;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,eAAe,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;QACzG;IACF;IAEA,gCAAgC;IAChC,MAAM,UAAU,IAAI,2CAA2C;IAC/D,IAAI,iBAAiB,SAAS;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,eAAe,4BAA4B,EAAE,QAAQ,8BAA8B,CAAC;QACtH;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAMO,SAAS;IACd,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AACnE;AAQO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAMO,SAAS;IACd,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,IAAI,kBAAkB,CAAC,SAAS;QAClD,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;IACvD,MAAM,uBAAuB,eAAe,cAAc,eAAe;IAEzE,OAAO,aAAa;AACtB;AAQO,SAAS,cAAiB,UAAkB,EAAE,YAAe;IAClE,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/utils/symbolMapping.ts"], "sourcesContent": ["// Symbol to token mapping for Angel One API\n// This maps trading symbols to their corresponding Angel One tokens\n\nexport interface StockMapping {\n  symbol: string;\n  name: string;\n  token: string;\n  exchange: 'NSE' | 'BSE';\n  type: 'STOCK' | 'INDEX';\n}\n\nexport const STOCK_MAPPINGS: Record<string, StockMapping> = {\n  'SBIN-EQ': { symbol: 'SBIN-EQ', name: 'State Bank of India', token: '3045', exchange: 'NSE', type: 'STOCK' },\n  'RELIANCE-EQ': { symbol: 'RELIANCE-EQ', name: 'Reliance Industries', token: '2885', exchange: 'NSE', type: 'STOCK' },\n  'TCS-EQ': { symbol: 'TCS-EQ', name: 'Tata Consultancy Services', token: '11536', exchange: 'NSE', type: 'STOCK' },\n  'INFY-EQ': { symbol: 'INFY-EQ', name: 'Infosys Limited', token: '1594', exchange: 'NSE', type: 'STOCK' },\n  'HDFCBANK-EQ': { symbol: 'HDFCBANK-EQ', name: 'HDFC Bank', token: '1333', exchange: 'NSE', type: 'STOCK' },\n  'ICICIBANK-EQ': { symbol: 'ICICIBANK-EQ', name: 'ICICI Bank', token: '4963', exchange: 'NSE', type: 'STOCK' },\n  'BHARTIARTL-EQ': { symbol: 'BHARTIARTL-EQ', name: 'Bharti Airtel', token: '10604', exchange: 'NSE', type: 'STOCK' },\n  'ITC-EQ': { symbol: 'ITC-EQ', name: 'ITC Limited', token: '424', exchange: 'NSE', type: 'STOCK' },\n  'KOTAKBANK-EQ': { symbol: 'KOTAKBANK-EQ', name: 'Kotak Mahindra Bank', token: '1922', exchange: 'NSE', type: 'STOCK' },\n  'LT-EQ': { symbol: 'LT-EQ', name: 'Larsen & Toubro', token: '11483', exchange: 'NSE', type: 'STOCK' },\n};\n\n// Market indices for benchmarking (replacing FD and Gold with real market data)\nexport const INDEX_MAPPINGS: Record<string, StockMapping> = {\n  'NIFTY': { symbol: 'NIFTY', name: 'Nifty 50', token: '********', exchange: 'NSE', type: 'INDEX' },\n  'BANKNIFTY': { symbol: 'BANKNIFTY', name: 'Bank Nifty', token: '********', exchange: 'NSE', type: 'INDEX' },\n  'NIFTYIT': { symbol: 'NIFTYIT', name: 'Nifty IT', token: '********', exchange: 'NSE', type: 'INDEX' },\n  'SENSEX': { symbol: 'SENSEX', name: 'BSE Sensex', token: '********', exchange: 'BSE', type: 'INDEX' },\n  'NIFTYNEXT50': { symbol: 'NIFTYNEXT50', name: 'Nifty Next 50', token: '********', exchange: 'NSE', type: 'INDEX' },\n};\n\n/**\n * Resolve a stock symbol to its Angel One token and exchange\n */\nexport function resolveStockSymbol(symbol: string): StockMapping {\n  const mapping = STOCK_MAPPINGS[symbol];\n  if (!mapping) {\n    throw new Error(`Unknown stock symbol: ${symbol}. Please use a supported symbol or add it to the mapping.`);\n  }\n  return mapping;\n}\n\n/**\n * Resolve an index symbol to its Angel One token and exchange\n */\nexport function resolveIndexSymbol(symbol: string): StockMapping {\n  const mapping = INDEX_MAPPINGS[symbol];\n  if (!mapping) {\n    throw new Error(`Unknown index symbol: ${symbol}. Please use a supported index or add it to the mapping.`);\n  }\n  return mapping;\n}\n\n/**\n * Resolve any symbol (stock or index) to its mapping\n */\nexport function resolveSymbol(symbol: string): StockMapping {\n  // Try stock mapping first\n  if (symbol in STOCK_MAPPINGS) {\n    return STOCK_MAPPINGS[symbol];\n  }\n\n  // Try index mapping\n  if (symbol in INDEX_MAPPINGS) {\n    return INDEX_MAPPINGS[symbol];\n  }\n\n  throw new Error(`Unknown symbol: ${symbol}. Please use a supported stock or index symbol.`);\n}\n\n/**\n * Check if a symbol is supported (stock or index)\n */\nexport function isSymbolSupported(symbol: string): boolean {\n  return symbol in STOCK_MAPPINGS || symbol in INDEX_MAPPINGS;\n}\n\n/**\n * Get all supported symbols (stocks and indices)\n */\nexport function getSupportedSymbols(): StockMapping[] {\n  return [...Object.values(STOCK_MAPPINGS), ...Object.values(INDEX_MAPPINGS)];\n}\n\n/**\n * Get all supported stock symbols\n */\nexport function getSupportedStocks(): StockMapping[] {\n  return Object.values(STOCK_MAPPINGS);\n}\n\n/**\n * Get all supported index symbols\n */\nexport function getSupportedIndices(): StockMapping[] {\n  return Object.values(INDEX_MAPPINGS);\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,oEAAoE;;;;;;;;;;;;AAU7D,MAAM,iBAA+C;IAC1D,WAAW;QAAE,QAAQ;QAAW,MAAM;QAAuB,OAAO;QAAQ,UAAU;QAAO,MAAM;IAAQ;IAC3G,eAAe;QAAE,QAAQ;QAAe,MAAM;QAAuB,OAAO;QAAQ,UAAU;QAAO,MAAM;IAAQ;IACnH,UAAU;QAAE,QAAQ;QAAU,MAAM;QAA6B,OAAO;QAAS,UAAU;QAAO,MAAM;IAAQ;IAChH,WAAW;QAAE,QAAQ;QAAW,MAAM;QAAmB,OAAO;QAAQ,UAAU;QAAO,MAAM;IAAQ;IACvG,eAAe;QAAE,QAAQ;QAAe,MAAM;QAAa,OAAO;QAAQ,UAAU;QAAO,MAAM;IAAQ;IACzG,gBAAgB;QAAE,QAAQ;QAAgB,MAAM;QAAc,OAAO;QAAQ,UAAU;QAAO,MAAM;IAAQ;IAC5G,iBAAiB;QAAE,QAAQ;QAAiB,MAAM;QAAiB,OAAO;QAAS,UAAU;QAAO,MAAM;IAAQ;IAClH,UAAU;QAAE,QAAQ;QAAU,MAAM;QAAe,OAAO;QAAO,UAAU;QAAO,MAAM;IAAQ;IAChG,gBAAgB;QAAE,QAAQ;QAAgB,MAAM;QAAuB,OAAO;QAAQ,UAAU;QAAO,MAAM;IAAQ;IACrH,SAAS;QAAE,QAAQ;QAAS,MAAM;QAAmB,OAAO;QAAS,UAAU;QAAO,MAAM;IAAQ;AACtG;AAGO,MAAM,iBAA+C;IAC1D,SAAS;QAAE,QAAQ;QAAS,MAAM;QAAY,OAAO;QAAY,UAAU;QAAO,MAAM;IAAQ;IAChG,aAAa;QAAE,QAAQ;QAAa,MAAM;QAAc,OAAO;QAAY,UAAU;QAAO,MAAM;IAAQ;IAC1G,WAAW;QAAE,QAAQ;QAAW,MAAM;QAAY,OAAO;QAAY,UAAU;QAAO,MAAM;IAAQ;IACpG,UAAU;QAAE,QAAQ;QAAU,MAAM;QAAc,OAAO;QAAY,UAAU;QAAO,MAAM;IAAQ;IACpG,eAAe;QAAE,QAAQ;QAAe,MAAM;QAAiB,OAAO;QAAY,UAAU;QAAO,MAAM;IAAQ;AACnH;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,UAAU,cAAc,CAAC,OAAO;IACtC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,OAAO,yDAAyD,CAAC;IAC5G;IACA,OAAO;AACT;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,UAAU,cAAc,CAAC,OAAO;IACtC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,OAAO,wDAAwD,CAAC;IAC3G;IACA,OAAO;AACT;AAKO,SAAS,cAAc,MAAc;IAC1C,0BAA0B;IAC1B,IAAI,UAAU,gBAAgB;QAC5B,OAAO,cAAc,CAAC,OAAO;IAC/B;IAEA,oBAAoB;IACpB,IAAI,UAAU,gBAAgB;QAC5B,OAAO,cAAc,CAAC,OAAO;IAC/B;IAEA,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,OAAO,+CAA+C,CAAC;AAC5F;AAKO,SAAS,kBAAkB,MAAc;IAC9C,OAAO,UAAU,kBAAkB,UAAU;AAC/C;AAKO,SAAS;IACd,OAAO;WAAI,OAAO,MAAM,CAAC;WAAoB,OAAO,MAAM,CAAC;KAAgB;AAC7E;AAKO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAKO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 1025, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/services/stockData.ts"], "sourcesContent": ["// Stock data service for the What If investment analysis tool\n\nimport { format, parseISO } from 'date-fns';\nimport { AngelOneClient } from '../api/angelone';\nimport { validateDateRange, calculateYearsBetweenDates } from '../utils';\nimport { resolveStockSymbol } from '../utils/symbolMapping';\nimport {\n  HistoricalPrice,\n  StockData,\n  InvestmentScenario,\n  InvestmentResult,\n  AngelOneHistoricalRequest,\n  AngelOneLTPRequest,\n} from '../types';\n\nexport class StockDataService {\n  private angelOneClient: AngelOneClient;\n\n  constructor(angelOneClient: AngelOneClient) {\n    this.angelOneClient = angelOneClient;\n  }\n\n  /**\n   * Get historical price data for a stock with automatic chunking for large date ranges\n   */\n  async getHistoricalPrices(\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE',\n    startDate: Date,\n    endDate: Date,\n    interval: 'ONE_DAY' = 'ONE_DAY'\n  ): Promise<HistoricalPrice[]> {\n    // Calculate date range in days\n    const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));\n\n    // Basic validation (without strict date range limits since we have chunking)\n    const basicValidation = this.validateBasicDateRange(startDate, endDate);\n    if (!basicValidation.isValid) {\n      throw new Error(basicValidation.error);\n    }\n\n    // If date range is larger than 2 years (730 days), chunk the requests\n    const MAX_DAYS_PER_CHUNK = 730; // 2 years\n\n    if (daysDifference <= MAX_DAYS_PER_CHUNK) {\n      // Single request for smaller ranges - apply full validation\n      const validation = validateDateRange(startDate, endDate);\n      if (!validation.isValid) {\n        throw new Error(validation.error);\n      }\n      return this.fetchSingleDateRange(symbolToken, exchange, startDate, endDate, interval);\n    } else {\n      // Chunk large date ranges - allow larger ranges with chunking\n      console.log(`📅 Large date range detected (${daysDifference} days). Chunking into smaller requests...`);\n      return this.fetchChunkedDateRange(symbolToken, exchange, startDate, endDate, interval, MAX_DAYS_PER_CHUNK);\n    }\n  }\n\n  /**\n   * Basic date validation without strict range limits\n   */\n  private validateBasicDateRange(startDate: Date, endDate: Date): {\n    isValid: boolean;\n    error?: string;\n  } {\n    const now = new Date();\n\n    if (startDate >= endDate) {\n      return { isValid: false, error: 'Start date must be before end date' };\n    }\n\n    if (startDate > now) {\n      return { isValid: false, error: 'Start date cannot be in the future' };\n    }\n\n    if (endDate > now) {\n      return { isValid: false, error: 'End date cannot be in the future' };\n    }\n\n    // Angel One API historical data limits (conservative)\n    const angelOneMinDate = new Date('2018-01-01');\n    if (startDate < angelOneMinDate) {\n      return {\n        isValid: false,\n        error: `Start date cannot be earlier than ${angelOneMinDate.toLocaleDateString()}. Angel One API historical data is limited to recent years.`\n      };\n    }\n\n    // Minimum date range validation\n    const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));\n    const minDays = 30; // At least 1 month for meaningful analysis\n    if (daysDifference < minDays) {\n      return {\n        isValid: false,\n        error: `Date range too small (${daysDifference} days). Minimum required is ${minDays} days for meaningful analysis.`\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Fetch historical data for a single date range\n   */\n  private async fetchSingleDateRange(\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE',\n    startDate: Date,\n    endDate: Date,\n    interval: 'ONE_DAY'\n  ): Promise<HistoricalPrice[]> {\n    const fromdate = format(startDate, 'yyyy-MM-dd HH:mm');\n    const todate = format(endDate, 'yyyy-MM-dd HH:mm');\n\n    const request: AngelOneHistoricalRequest = {\n      exchange,\n      symboltoken: symbolToken,\n      interval,\n      fromdate,\n      todate,\n    };\n\n    try {\n      console.log(`📊 Fetching data: ${fromdate} to ${todate}`);\n      const historicalData = await this.angelOneClient.getHistoricalData(request);\n\n      // Sort by date ascending\n      return historicalData.sort((a, b) => a.date.getTime() - b.date.getTime());\n    } catch (error) {\n      console.error('Error fetching historical data:', error);\n      throw new Error(`Failed to fetch historical data: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Fetch historical data in chunks for large date ranges\n   */\n  private async fetchChunkedDateRange(\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE',\n    startDate: Date,\n    endDate: Date,\n    interval: 'ONE_DAY',\n    maxDaysPerChunk: number\n  ): Promise<HistoricalPrice[]> {\n    const allData: HistoricalPrice[] = [];\n    let currentStart = new Date(startDate);\n    let chunkNumber = 1;\n\n    while (currentStart < endDate) {\n      // Calculate chunk end date\n      const chunkEnd = new Date(currentStart);\n      chunkEnd.setDate(chunkEnd.getDate() + maxDaysPerChunk);\n\n      // Don't exceed the actual end date\n      if (chunkEnd > endDate) {\n        chunkEnd.setTime(endDate.getTime());\n      }\n\n      console.log(`📦 Fetching chunk ${chunkNumber}: ${format(currentStart, 'yyyy-MM-dd')} to ${format(chunkEnd, 'yyyy-MM-dd')}`);\n\n      try {\n        const chunkData = await this.fetchSingleDateRange(symbolToken, exchange, currentStart, chunkEnd, interval);\n        allData.push(...chunkData);\n\n        // Add a small delay between requests to avoid rate limiting\n        await new Promise(resolve => setTimeout(resolve, 100));\n\n      } catch (error) {\n        console.error(`Error fetching chunk ${chunkNumber}:`, error);\n        // Continue with next chunk instead of failing completely\n        console.log(`⚠️ Skipping chunk ${chunkNumber} due to error, continuing...`);\n      }\n\n      // Move to next chunk\n      currentStart = new Date(chunkEnd);\n      currentStart.setDate(currentStart.getDate() + 1);\n      chunkNumber++;\n    }\n\n    if (allData.length === 0) {\n      throw new Error('No historical data could be fetched for any date range');\n    }\n\n    console.log(`✅ Successfully fetched ${allData.length} data points across ${chunkNumber - 1} chunks`);\n\n    // Remove duplicates and sort by date\n    const uniqueData = allData.filter((item, index, arr) =>\n      index === arr.findIndex(other => other.date.getTime() === item.date.getTime())\n    );\n\n    return uniqueData.sort((a, b) => a.date.getTime() - b.date.getTime());\n  }\n\n  /**\n   * Get current stock price\n   */\n  async getCurrentPrice(\n    tradingSymbol: string,\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE'\n  ): Promise<StockData> {\n    const request: AngelOneLTPRequest = {\n      exchange,\n      tradingsymbol: tradingSymbol,\n      symboltoken: symbolToken,\n    };\n\n    try {\n      return await this.angelOneClient.getCurrentPrice(request);\n    } catch (error) {\n      console.error('Error fetching current price:', error);\n      throw new Error(`Failed to fetch current price: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Calculate investment result for a scenario\n   */\n  async calculateInvestmentResult(scenario: InvestmentScenario): Promise<InvestmentResult> {\n    try {\n      // Resolve stock symbol to token and exchange\n      const stockMapping = resolveStockSymbol(scenario.stockSymbol);\n      console.log(`📊 Resolved ${scenario.stockSymbol} to token ${stockMapping.token} on ${stockMapping.exchange}`);\n\n      // Get historical data for the investment period\n      const historicalData = await this.getHistoricalPrices(\n        stockMapping.token, // Use the resolved token\n        stockMapping.exchange, // Use the resolved exchange\n        scenario.startDate,\n        scenario.endDate\n      );\n\n      if (historicalData.length === 0) {\n        throw new Error('No historical data available for the specified period');\n      }\n\n      // Get start and end prices\n      const startPrice = historicalData[0].close;\n      const endPrice = historicalData[historicalData.length - 1].close;\n\n      // Calculate number of shares that could be bought\n      const numberOfShares = scenario.investmentAmount / startPrice;\n\n      // Calculate current value\n      const currentValue = numberOfShares * endPrice;\n\n      // Calculate returns\n      const absoluteReturn = ((currentValue - scenario.investmentAmount) / scenario.investmentAmount) * 100;\n      const years = calculateYearsBetweenDates(scenario.startDate, scenario.endDate);\n      const cagr = years > 0 ? (Math.pow(currentValue / scenario.investmentAmount, 1 / years) - 1) * 100 : 0;\n\n      return {\n        scenario,\n        initialValue: scenario.investmentAmount,\n        currentValue,\n        absoluteReturn,\n        cagr: Number(cagr.toFixed(2)),\n        totalReturn: currentValue - scenario.investmentAmount,\n        annualizedReturn: cagr,\n      };\n    } catch (error) {\n      console.error('Error calculating investment result:', error);\n      throw new Error(`Failed to calculate investment result: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Get price at a specific date (or closest available date)\n   */\n  async getPriceAtDate(\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE',\n    targetDate: Date\n  ): Promise<{ price: number; actualDate: Date } | null> {\n    try {\n      // Get data for a small range around the target date\n      const startDate = new Date(targetDate);\n      startDate.setDate(startDate.getDate() - 5); // 5 days before\n\n      const endDate = new Date(targetDate);\n      endDate.setDate(endDate.getDate() + 5); // 5 days after\n\n      const historicalData = await this.getHistoricalPrices(\n        symbolToken,\n        exchange,\n        startDate,\n        endDate\n      );\n\n      if (historicalData.length === 0) {\n        return null;\n      }\n\n      // Find the closest date\n      let closestData = historicalData[0];\n      let minDiff = Math.abs(closestData.date.getTime() - targetDate.getTime());\n\n      for (const data of historicalData) {\n        const diff = Math.abs(data.date.getTime() - targetDate.getTime());\n        if (diff < minDiff) {\n          minDiff = diff;\n          closestData = data;\n        }\n      }\n\n      return {\n        price: closestData.close,\n        actualDate: closestData.date,\n      };\n    } catch (error) {\n      console.error('Error getting price at date:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Validate stock symbol and get basic info\n   */\n  async validateStock(\n    tradingSymbol: string,\n    symbolToken: string,\n    exchange: 'NSE' | 'BSE'\n  ): Promise<{ isValid: boolean; stockData?: StockData; error?: string }> {\n    try {\n      const stockData = await this.getCurrentPrice(tradingSymbol, symbolToken, exchange);\n      return {\n        isValid: true,\n        stockData,\n      };\n    } catch (error) {\n      return {\n        isValid: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n      };\n    }\n  }\n\n  /**\n   * Get multiple stocks' current prices\n   */\n  async getMultipleCurrentPrices(\n    stocks: Array<{\n      tradingSymbol: string;\n      symbolToken: string;\n      exchange: 'NSE' | 'BSE';\n    }>\n  ): Promise<StockData[]> {\n    const results: StockData[] = [];\n    \n    // Process stocks sequentially to respect rate limits\n    for (const stock of stocks) {\n      try {\n        const stockData = await this.getCurrentPrice(\n          stock.tradingSymbol,\n          stock.symbolToken,\n          stock.exchange\n        );\n        results.push(stockData);\n      } catch (error) {\n        console.error(`Error fetching price for ${stock.tradingSymbol}:`, error);\n        // Continue with other stocks even if one fails\n      }\n    }\n\n    return results;\n  }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AAE9D;AAEA;AACA;;;;AAUO,MAAM;IACH,eAA+B;IAEvC,YAAY,cAA8B,CAAE;QAC1C,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA;;GAEC,GACD,MAAM,oBACJ,WAAmB,EACnB,QAAuB,EACvB,SAAe,EACf,OAAa,EACb,WAAsB,SAAS,EACH;QAC5B,+BAA+B;QAC/B,MAAM,iBAAiB,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAEjG,6EAA6E;QAC7E,MAAM,kBAAkB,IAAI,CAAC,sBAAsB,CAAC,WAAW;QAC/D,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;QAEA,sEAAsE;QACtE,MAAM,qBAAqB,KAAK,UAAU;QAE1C,IAAI,kBAAkB,oBAAoB;YACxC,4DAA4D;YAC5D,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;YAChD,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,KAAK;YAClC;YACA,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,UAAU,WAAW,SAAS;QAC9E,OAAO;YACL,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,eAAe,yCAAyC,CAAC;YACtG,OAAO,IAAI,CAAC,qBAAqB,CAAC,aAAa,UAAU,WAAW,SAAS,UAAU;QACzF;IACF;IAEA;;GAEC,GACD,AAAQ,uBAAuB,SAAe,EAAE,OAAa,EAG3D;QACA,MAAM,MAAM,IAAI;QAEhB,IAAI,aAAa,SAAS;YACxB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QAEA,IAAI,YAAY,KAAK;YACnB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QAEA,IAAI,UAAU,KAAK;YACjB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;QAEA,sDAAsD;QACtD,MAAM,kBAAkB,IAAI,KAAK;QACjC,IAAI,YAAY,iBAAiB;YAC/B,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,kCAAkC,EAAE,gBAAgB,kBAAkB,GAAG,2DAA2D,CAAC;YAC/I;QACF;QAEA,gCAAgC;QAChC,MAAM,iBAAiB,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QACjG,MAAM,UAAU,IAAI,2CAA2C;QAC/D,IAAI,iBAAiB,SAAS;YAC5B,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,sBAAsB,EAAE,eAAe,4BAA4B,EAAE,QAAQ,8BAA8B,CAAC;YACtH;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,MAAc,qBACZ,WAAmB,EACnB,QAAuB,EACvB,SAAe,EACf,OAAa,EACb,QAAmB,EACS;QAC5B,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QACnC,MAAM,SAAS,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QAE/B,MAAM,UAAqC;YACzC;YACA,aAAa;YACb;YACA;YACA;QACF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS,IAAI,EAAE,QAAQ;YACxD,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;YAEnE,yBAAyB;YACzB,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO;QACxE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAChH;IACF;IAEA;;GAEC,GACD,MAAc,sBACZ,WAAmB,EACnB,QAAuB,EACvB,SAAe,EACf,OAAa,EACb,QAAmB,EACnB,eAAuB,EACK;QAC5B,MAAM,UAA6B,EAAE;QACrC,IAAI,eAAe,IAAI,KAAK;QAC5B,IAAI,cAAc;QAElB,MAAO,eAAe,QAAS;YAC7B,2BAA2B;YAC3B,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YAEtC,mCAAmC;YACnC,IAAI,WAAW,SAAS;gBACtB,SAAS,OAAO,CAAC,QAAQ,OAAO;YAClC;YAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,YAAY,EAAE,EAAE,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,cAAc,cAAc,IAAI,EAAE,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,eAAe;YAE1H,IAAI;gBACF,MAAM,YAAY,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,UAAU,cAAc,UAAU;gBACjG,QAAQ,IAAI,IAAI;gBAEhB,4DAA4D;gBAC5D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;gBACtD,yDAAyD;gBACzD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,YAAY,4BAA4B,CAAC;YAC5E;YAEA,qBAAqB;YACrB,eAAe,IAAI,KAAK;YACxB,aAAa,OAAO,CAAC,aAAa,OAAO,KAAK;YAC9C;QACF;QAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,MAAM,CAAC,oBAAoB,EAAE,cAAc,EAAE,OAAO,CAAC;QAEnG,qCAAqC;QACrC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAC,MAAM,OAAO,MAC9C,UAAU,IAAI,SAAS,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,OAAO,OAAO,KAAK,IAAI,CAAC,OAAO;QAG7E,OAAO,WAAW,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO;IACpE;IAEA;;GAEC,GACD,MAAM,gBACJ,aAAqB,EACrB,WAAmB,EACnB,QAAuB,EACH;QACpB,MAAM,UAA8B;YAClC;YACA,eAAe;YACf,aAAa;QACf;QAEA,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC9G;IACF;IAEA;;GAEC,GACD,MAAM,0BAA0B,QAA4B,EAA6B;QACvF,IAAI;YACF,6CAA6C;YAC7C,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,WAAW;YAC5D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS,WAAW,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,IAAI,EAAE,aAAa,QAAQ,EAAE;YAE5G,gDAAgD;YAChD,MAAM,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,CACnD,aAAa,KAAK,EAClB,aAAa,QAAQ,EACrB,SAAS,SAAS,EAClB,SAAS,OAAO;YAGlB,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,aAAa,cAAc,CAAC,EAAE,CAAC,KAAK;YAC1C,MAAM,WAAW,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;YAEhE,kDAAkD;YAClD,MAAM,iBAAiB,SAAS,gBAAgB,GAAG;YAEnD,0BAA0B;YAC1B,MAAM,eAAe,iBAAiB;YAEtC,oBAAoB;YACpB,MAAM,iBAAiB,AAAC,CAAC,eAAe,SAAS,gBAAgB,IAAI,SAAS,gBAAgB,GAAI;YAClG,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,SAAS,EAAE,SAAS,OAAO;YAC7E,MAAM,OAAO,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,eAAe,SAAS,gBAAgB,EAAE,IAAI,SAAS,CAAC,IAAI,MAAM;YAErG,OAAO;gBACL;gBACA,cAAc,SAAS,gBAAgB;gBACvC;gBACA;gBACA,MAAM,OAAO,KAAK,OAAO,CAAC;gBAC1B,aAAa,eAAe,SAAS,gBAAgB;gBACrD,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM,CAAC,uCAAuC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACtH;IACF;IAEA;;GAEC,GACD,MAAM,eACJ,WAAmB,EACnB,QAAuB,EACvB,UAAgB,EACqC;QACrD,IAAI;YACF,oDAAoD;YACpD,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK,IAAI,gBAAgB;YAE5D,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,eAAe;YAEvD,MAAM,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,CACnD,aACA,UACA,WACA;YAGF,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,OAAO;YACT;YAEA,wBAAwB;YACxB,IAAI,cAAc,cAAc,CAAC,EAAE;YACnC,IAAI,UAAU,KAAK,GAAG,CAAC,YAAY,IAAI,CAAC,OAAO,KAAK,WAAW,OAAO;YAEtE,KAAK,MAAM,QAAQ,eAAgB;gBACjC,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,OAAO,KAAK,WAAW,OAAO;gBAC9D,IAAI,OAAO,SAAS;oBAClB,UAAU;oBACV,cAAc;gBAChB;YACF;YAEA,OAAO;gBACL,OAAO,YAAY,KAAK;gBACxB,YAAY,YAAY,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,cACJ,aAAqB,EACrB,WAAmB,EACnB,QAAuB,EAC+C;QACtE,IAAI;YACF,MAAM,YAAY,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,aAAa;YACzE,OAAO;gBACL,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,yBACJ,MAIE,EACoB;QACtB,MAAM,UAAuB,EAAE;QAE/B,qDAAqD;QACrD,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI;gBACF,MAAM,YAAY,MAAM,IAAI,CAAC,eAAe,CAC1C,MAAM,aAAa,EACnB,MAAM,WAAW,EACjB,MAAM,QAAQ;gBAEhB,QAAQ,IAAI,CAAC;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,aAAa,CAAC,CAAC,CAAC,EAAE;YAClE,+CAA+C;YACjD;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/services/benchmarkData.ts"], "sourcesContent": ["// Benchmark data service for the What If investment analysis tool\n// Now uses real market indices instead of simulated FD/Gold data\n\nimport { AngelOneClient } from '../api/angelone';\nimport { BENCHMARK_CONFIG } from '../config';\nimport { calculateCAGR, calculateAbsoluteReturn, calculateYearsBetweenDates } from '../utils';\nimport { resolveIndexSymbol, getSupportedIndices } from '../utils/symbolMapping';\nimport { BenchmarkData, AngelOneHistoricalRequest, HistoricalPrice } from '../types';\nimport { format } from 'date-fns';\n\nexport class BenchmarkDataService {\n  private angelOneClient: AngelOneClient;\n\n  constructor(angelOneClient: AngelOneClient) {\n    this.angelOneClient = angelOneClient;\n  }\n\n  /**\n   * Get real market index data (replaces simulated FD/Gold)\n   */\n  async getIndexData(indexSymbol: string, startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    try {\n      const indexMapping = resolveIndexSymbol(indexSymbol);\n\n      const request: AngelOneHistoricalRequest = {\n        exchange: indexMapping.exchange,\n        symboltoken: indexMapping.token,\n        interval: 'ONE_DAY',\n        fromdate: format(startDate, 'yyyy-MM-dd HH:mm'),\n        todate: format(endDate, 'yyyy-MM-dd HH:mm'),\n      };\n\n      console.log(`📊 Fetching ${indexMapping.name} data from ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`);\n\n      const historicalData = await this.angelOneClient.getHistoricalData(request);\n\n      if (historicalData.length === 0) {\n        throw new Error(`No historical data available for ${indexMapping.name}`);\n      }\n\n      const startPrice = historicalData[0].close;\n      const endPrice = historicalData[historicalData.length - 1].close;\n      const years = calculateYearsBetweenDates(startDate, endDate);\n\n      return {\n        type: indexSymbol as any,\n        name: indexMapping.name,\n        startDate,\n        endDate,\n        startValue: startPrice,\n        endValue: endPrice,\n        cagr: calculateCAGR(startPrice, endPrice, years),\n        absoluteReturn: calculateAbsoluteReturn(startPrice, endPrice),\n        historicalData: historicalData.map(item => ({\n          date: item.date,\n          value: item.close,\n        })),\n      };\n    } catch (error) {\n      console.error(`Error fetching ${indexSymbol} data:`, error);\n      throw new Error(`Failed to fetch ${indexSymbol} data: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Get Nifty 50 historical data (updated to use new structure)\n   */\n  async getNiftyData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    return this.getIndexData('NIFTY', startDate, endDate);\n  }\n\n  /**\n   * Get Bank Nifty historical data (replaces Gold)\n   */\n  async getBankNiftyData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    return this.getIndexData('BANKNIFTY', startDate, endDate);\n  }\n\n  /**\n   * Get Nifty IT historical data (replaces Fixed Deposit)\n   */\n  async getNiftyITData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    return this.getIndexData('NIFTYIT', startDate, endDate);\n  }\n\n  /**\n   * Get BSE Sensex historical data (additional benchmark)\n   */\n  async getSensexData(startDate: Date, endDate: Date): Promise<BenchmarkData> {\n    return this.getIndexData('SENSEX', startDate, endDate);\n  }\n\n  /**\n   * Get all benchmark data for comparison (now uses real market indices)\n   */\n  async getAllBenchmarks(startDate: Date, endDate: Date): Promise<{\n    nifty: BenchmarkData;\n    bankNifty: BenchmarkData;\n    niftyIT: BenchmarkData;\n  }> {\n    try {\n      console.log('📊 Fetching all benchmark data with real market indices...');\n\n      const [nifty, bankNifty, niftyIT] = await Promise.all([\n        this.getNiftyData(startDate, endDate),\n        this.getBankNiftyData(startDate, endDate),\n        this.getNiftyITData(startDate, endDate),\n      ]);\n\n      return { nifty, bankNifty, niftyIT };\n    } catch (error) {\n      console.error('Error fetching benchmark data:', error);\n      throw new Error(`Failed to fetch benchmark data: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Calculate benchmark returns for a given investment amount and period\n   * Now supports real market indices instead of simulated data\n   */\n  async calculateBenchmarkReturns(\n    benchmarkType: 'NIFTY' | 'BANKNIFTY' | 'NIFTYIT' | 'SENSEX',\n    investmentAmount: number,\n    startDate: Date,\n    endDate: Date\n  ): Promise<{\n    initialValue: number;\n    currentValue: number;\n    cagr: number;\n    absoluteReturn: number;\n  }> {\n    let benchmarkData: BenchmarkData;\n\n    switch (benchmarkType) {\n      case 'NIFTY':\n        benchmarkData = await this.getNiftyData(startDate, endDate);\n        break;\n      case 'BANKNIFTY':\n        benchmarkData = await this.getBankNiftyData(startDate, endDate);\n        break;\n      case 'NIFTYIT':\n        benchmarkData = await this.getNiftyITData(startDate, endDate);\n        break;\n      case 'SENSEX':\n        benchmarkData = await this.getSensexData(startDate, endDate);\n        break;\n      default:\n        throw new Error(`Unsupported benchmark type: ${benchmarkType}`);\n    }\n\n    if (benchmarkData.historicalData.length === 0) {\n      throw new Error(`No data available for ${benchmarkType} in the specified period`);\n    }\n\n    const startValue = benchmarkData.startValue;\n    const endValue = benchmarkData.endValue;\n\n    // Calculate how much of the benchmark could be bought with the investment amount\n    const units = investmentAmount / startValue;\n    const currentValue = units * endValue;\n\n    return {\n      initialValue: investmentAmount,\n      currentValue,\n      cagr: benchmarkData.cagr,\n      absoluteReturn: benchmarkData.absoluteReturn,\n    };\n  }\n\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;AAClE,iEAAiE;;;;AAIjE;AACA;AAEA;;;;AAEO,MAAM;IACH,eAA+B;IAEvC,YAAY,cAA8B,CAAE;QAC1C,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA;;GAEC,GACD,MAAM,aAAa,WAAmB,EAAE,SAAe,EAAE,OAAa,EAA0B;QAC9F,IAAI;YACF,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,MAAM,UAAqC;gBACzC,UAAU,aAAa,QAAQ;gBAC/B,aAAa,aAAa,KAAK;gBAC/B,UAAU;gBACV,UAAU,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;gBAC5B,QAAQ,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;YAC1B;YAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,aAAa,IAAI,CAAC,WAAW,EAAE,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,cAAc,IAAI,EAAE,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,eAAe;YAE/H,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;YAEnE,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,aAAa,IAAI,EAAE;YACzE;YAEA,MAAM,aAAa,cAAc,CAAC,EAAE,CAAC,KAAK;YAC1C,MAAM,WAAW,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;YAChE,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;YAEpD,OAAO;gBACL,MAAM;gBACN,MAAM,aAAa,IAAI;gBACvB;gBACA;gBACA,YAAY;gBACZ,UAAU;gBACV,MAAM,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,UAAU;gBAC1C,gBAAgB,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,YAAY;gBACpD,gBAAgB,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC1C,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;oBACnB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,YAAY,MAAM,CAAC,EAAE;YACrD,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,YAAY,OAAO,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACpH;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,SAAe,EAAE,OAAa,EAA0B;QACzE,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW;IAC/C;IAEA;;GAEC,GACD,MAAM,iBAAiB,SAAe,EAAE,OAAa,EAA0B;QAC7E,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,WAAW;IACnD;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAA0B;QAC3E,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,WAAW;IACjD;IAEA;;GAEC,GACD,MAAM,cAAc,SAAe,EAAE,OAAa,EAA0B;QAC1E,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,WAAW;IAChD;IAEA;;GAEC,GACD,MAAM,iBAAiB,SAAe,EAAE,OAAa,EAIlD;QACD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,CAAC,OAAO,WAAW,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpD,IAAI,CAAC,YAAY,CAAC,WAAW;gBAC7B,IAAI,CAAC,gBAAgB,CAAC,WAAW;gBACjC,IAAI,CAAC,cAAc,CAAC,WAAW;aAChC;YAED,OAAO;gBAAE;gBAAO;gBAAW;YAAQ;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC/G;IACF;IAEA;;;GAGC,GACD,MAAM,0BACJ,aAA2D,EAC3D,gBAAwB,EACxB,SAAe,EACf,OAAa,EAMZ;QACD,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,gBAAgB,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW;gBACnD;YACF,KAAK;gBACH,gBAAgB,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW;gBACvD;YACF,KAAK;gBACH,gBAAgB,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW;gBACrD;YACF,KAAK;gBACH,gBAAgB,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW;gBACpD;YACF;gBACE,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,eAAe;QAClE;QAEA,IAAI,cAAc,cAAc,CAAC,MAAM,KAAK,GAAG;YAC7C,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,cAAc,wBAAwB,CAAC;QAClF;QAEA,MAAM,aAAa,cAAc,UAAU;QAC3C,MAAM,WAAW,cAAc,QAAQ;QAEvC,iFAAiF;QACjF,MAAM,QAAQ,mBAAmB;QACjC,MAAM,eAAe,QAAQ;QAE7B,OAAO;YACL,cAAc;YACd;YACA,MAAM,cAAc,IAAI;YACxB,gBAAgB,cAAc,cAAc;QAC9C;IACF;AAEF", "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/services/investmentCalculator.ts"], "sourcesContent": ["// Investment calculation engine for the What If investment analysis tool\n\nimport { StockDataService } from './stockData';\nimport { BenchmarkDataService } from './benchmarkData';\nimport { \n  calculateCAGR, \n  calculateAbsoluteReturn, \n  calculateYearsBetweenDates,\n  validateDateRange,\n  generateId \n} from '../utils';\nimport {\n  InvestmentScenario,\n  InvestmentResult,\n  ComparisonResult,\n  HistoricalPrice,\n} from '../types';\n\nexport class InvestmentCalculator {\n  private stockDataService: StockDataService;\n  private benchmarkDataService: BenchmarkDataService;\n\n  constructor(\n    stockDataService: StockDataService,\n    benchmarkDataService: BenchmarkDataService\n  ) {\n    this.stockDataService = stockDataService;\n    this.benchmarkDataService = benchmarkDataService;\n  }\n\n  /**\n   * Create a new investment scenario\n   */\n  createScenario(\n    stockSymbol: string,\n    investmentAmount: number,\n    startDate: Date,\n    endDate: Date = new Date()\n  ): InvestmentScenario {\n    // Validate inputs\n    if (investmentAmount <= 0) {\n      throw new Error('Investment amount must be greater than 0');\n    }\n\n    const validation = validateDateRange(startDate, endDate);\n    if (!validation.isValid) {\n      throw new Error(validation.error);\n    }\n\n    return {\n      id: generateId(),\n      stockSymbol,\n      investmentAmount,\n      startDate,\n      endDate,\n      createdAt: new Date(),\n    };\n  }\n\n  /**\n   * Calculate investment result for a scenario\n   */\n  async calculateInvestmentResult(scenario: InvestmentScenario): Promise<InvestmentResult> {\n    try {\n      return await this.stockDataService.calculateInvestmentResult(scenario);\n    } catch (error) {\n      console.error('Error calculating investment result:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate real benchmark returns using market indices\n   */\n  private async calculateRealBenchmarkReturns(\n    investmentAmount: number,\n    startDate: Date,\n    endDate: Date\n  ): Promise<Record<string, {\n    initialValue: number;\n    currentValue: number;\n    cagr: number;\n    absoluteReturn: number;\n  }>> {\n    const results: Record<string, {\n      initialValue: number;\n      currentValue: number;\n      cagr: number;\n      absoluteReturn: number;\n    }> = {};\n\n    // Use real market indices instead of simulated FD/Gold\n    const benchmarks = ['NIFTY', 'BANKNIFTY', 'NIFTYIT'] as const;\n\n    // Process benchmarks sequentially to avoid overwhelming the API\n    for (const benchmark of benchmarks) {\n      try {\n        console.log(`📊 Calculating ${benchmark} benchmark returns...`);\n        results[benchmark] = await this.benchmarkDataService.calculateBenchmarkReturns(\n          benchmark,\n          investmentAmount,\n          startDate,\n          endDate\n        );\n      } catch (error) {\n        console.error(`Error calculating ${benchmark} returns:`, error);\n        // Continue with other benchmarks even if one fails\n        results[benchmark] = {\n          initialValue: investmentAmount,\n          currentValue: investmentAmount,\n          cagr: 0,\n          absoluteReturn: 0,\n        };\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * Calculate investment result with benchmark comparisons\n   */\n  async calculateWithComparisons(scenario: InvestmentScenario): Promise<ComparisonResult> {\n    try {\n      // Calculate the main investment result\n      const investmentResult = await this.calculateInvestmentResult(scenario);\n\n      // Calculate benchmark returns using real market indices\n      const benchmarkReturns = await this.calculateRealBenchmarkReturns(\n        scenario.investmentAmount,\n        scenario.startDate,\n        scenario.endDate\n      );\n\n      return {\n        investment: investmentResult,\n        benchmarks: benchmarkReturns,\n      };\n    } catch (error) {\n      console.error('Error calculating investment with comparisons:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate returns for multiple investment amounts (sensitivity analysis)\n   */\n  async calculateSensitivityAnalysis(\n    stockSymbol: string,\n    baseAmount: number,\n    startDate: Date,\n    endDate: Date,\n    variations: number[] = [0.5, 0.75, 1, 1.25, 1.5, 2] // Multipliers\n  ): Promise<Array<{\n    amount: number;\n    result: InvestmentResult;\n  }>> {\n    const results: Array<{ amount: number; result: InvestmentResult }> = [];\n\n    for (const multiplier of variations) {\n      const amount = baseAmount * multiplier;\n      const scenario = this.createScenario(stockSymbol, amount, startDate, endDate);\n      \n      try {\n        const result = await this.calculateInvestmentResult(scenario);\n        results.push({ amount, result });\n      } catch (error) {\n        console.error(`Error calculating for amount ${amount}:`, error);\n        // Continue with other amounts\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * Calculate returns for different time periods (time-based analysis)\n   */\n  async calculateTimeBasedAnalysis(\n    stockSymbol: string,\n    investmentAmount: number,\n    baseStartDate: Date,\n    periods: Array<{ label: string; months: number }> = [\n      { label: '6 months', months: 6 },\n      { label: '1 year', months: 12 },\n      { label: '2 years', months: 24 },\n      { label: '3 years', months: 36 },\n      { label: '5 years', months: 60 },\n    ]\n  ): Promise<Array<{\n    period: string;\n    startDate: Date;\n    endDate: Date;\n    result: InvestmentResult;\n  }>> {\n    const results: Array<{\n      period: string;\n      startDate: Date;\n      endDate: Date;\n      result: InvestmentResult;\n    }> = [];\n\n    for (const period of periods) {\n      const startDate = new Date(baseStartDate);\n      const endDate = new Date(baseStartDate);\n      endDate.setMonth(endDate.getMonth() + period.months);\n\n      // Don't calculate for future dates\n      if (endDate > new Date()) {\n        continue;\n      }\n\n      const scenario = this.createScenario(stockSymbol, investmentAmount, startDate, endDate);\n      \n      try {\n        const result = await this.calculateInvestmentResult(scenario);\n        results.push({\n          period: period.label,\n          startDate,\n          endDate,\n          result,\n        });\n      } catch (error) {\n        console.error(`Error calculating for period ${period.label}:`, error);\n        // Continue with other periods\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * Calculate SIP (Systematic Investment Plan) returns\n   */\n  async calculateSIPReturns(\n    stockSymbol: string,\n    monthlyAmount: number,\n    startDate: Date,\n    endDate: Date\n  ): Promise<{\n    totalInvested: number;\n    currentValue: number;\n    totalReturn: number;\n    cagr: number;\n    absoluteReturn: number;\n    installments: Array<{\n      date: Date;\n      amount: number;\n      price: number;\n      units: number;\n      cumulativeUnits: number;\n      cumulativeInvestment: number;\n    }>;\n  }> {\n    const installments: Array<{\n      date: Date;\n      amount: number;\n      price: number;\n      units: number;\n      cumulativeUnits: number;\n      cumulativeInvestment: number;\n    }> = [];\n\n    let totalInvested = 0;\n    let totalUnits = 0;\n    const currentDate = new Date(startDate);\n\n    // Calculate monthly investments\n    while (currentDate <= endDate) {\n      try {\n        // Get price at this date (or closest available)\n        const priceData = await this.stockDataService.getPriceAtDate(\n          stockSymbol,\n          'NSE', // Default to NSE\n          currentDate\n        );\n\n        if (priceData) {\n          const units = monthlyAmount / priceData.price;\n          totalUnits += units;\n          totalInvested += monthlyAmount;\n\n          installments.push({\n            date: new Date(currentDate),\n            amount: monthlyAmount,\n            price: priceData.price,\n            units,\n            cumulativeUnits: totalUnits,\n            cumulativeInvestment: totalInvested,\n          });\n        }\n      } catch (error) {\n        console.error(`Error calculating SIP for ${currentDate}:`, error);\n        // Continue with next month\n      }\n\n      // Move to next month\n      currentDate.setMonth(currentDate.getMonth() + 1);\n    }\n\n    if (installments.length === 0) {\n      throw new Error('No SIP installments could be calculated');\n    }\n\n    // Get current price to calculate current value\n    try {\n      const currentPriceData = await this.stockDataService.getCurrentPrice(\n        stockSymbol.split('-')[0], // Extract symbol from token\n        stockSymbol,\n        'NSE'\n      );\n\n      const currentValue = totalUnits * currentPriceData.currentPrice;\n      const totalReturn = currentValue - totalInvested;\n      const years = calculateYearsBetweenDates(startDate, endDate);\n      const cagr = calculateCAGR(totalInvested, currentValue, years);\n      const absoluteReturn = calculateAbsoluteReturn(totalInvested, currentValue);\n\n      return {\n        totalInvested,\n        currentValue,\n        totalReturn,\n        cagr,\n        absoluteReturn,\n        installments,\n      };\n    } catch (error) {\n      throw new Error(`Failed to calculate SIP returns: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Calculate optimal investment timing (dollar-cost averaging analysis)\n   */\n  async calculateOptimalTiming(\n    stockSymbol: string,\n    totalAmount: number,\n    startDate: Date,\n    endDate: Date,\n    strategies: Array<{\n      name: string;\n      type: 'lump_sum' | 'monthly_sip' | 'quarterly_sip';\n    }> = [\n      { name: 'Lump Sum at Start', type: 'lump_sum' },\n      { name: 'Monthly SIP', type: 'monthly_sip' },\n      { name: 'Quarterly SIP', type: 'quarterly_sip' },\n    ]\n  ): Promise<Array<{\n    strategy: string;\n    totalInvested: number;\n    currentValue: number;\n    cagr: number;\n    absoluteReturn: number;\n  }>> {\n    const results: Array<{\n      strategy: string;\n      totalInvested: number;\n      currentValue: number;\n      cagr: number;\n      absoluteReturn: number;\n    }> = [];\n\n    for (const strategy of strategies) {\n      try {\n        let result;\n\n        switch (strategy.type) {\n          case 'lump_sum':\n            const scenario = this.createScenario(stockSymbol, totalAmount, startDate, endDate);\n            const lumpSumResult = await this.calculateInvestmentResult(scenario);\n            result = {\n              strategy: strategy.name,\n              totalInvested: lumpSumResult.initialValue,\n              currentValue: lumpSumResult.currentValue,\n              cagr: lumpSumResult.cagr,\n              absoluteReturn: lumpSumResult.absoluteReturn,\n            };\n            break;\n\n          case 'monthly_sip':\n            const months = Math.ceil(calculateYearsBetweenDates(startDate, endDate) * 12);\n            const monthlyAmount = totalAmount / months;\n            const monthlySipResult = await this.calculateSIPReturns(\n              stockSymbol,\n              monthlyAmount,\n              startDate,\n              endDate\n            );\n            result = {\n              strategy: strategy.name,\n              totalInvested: monthlySipResult.totalInvested,\n              currentValue: monthlySipResult.currentValue,\n              cagr: monthlySipResult.cagr,\n              absoluteReturn: monthlySipResult.absoluteReturn,\n            };\n            break;\n\n          case 'quarterly_sip':\n            const quarters = Math.ceil(calculateYearsBetweenDates(startDate, endDate) * 4);\n            const quarterlyAmount = totalAmount / quarters;\n            // For quarterly, we'll simulate with 3-month intervals\n            const quarterlySipResult = await this.calculateSIPReturns(\n              stockSymbol,\n              quarterlyAmount,\n              startDate,\n              endDate\n            );\n            result = {\n              strategy: strategy.name,\n              totalInvested: quarterlySipResult.totalInvested,\n              currentValue: quarterlySipResult.currentValue,\n              cagr: quarterlySipResult.cagr,\n              absoluteReturn: quarterlySipResult.absoluteReturn,\n            };\n            break;\n\n          default:\n            continue;\n        }\n\n        results.push(result);\n      } catch (error) {\n        console.error(`Error calculating ${strategy.name}:`, error);\n        // Continue with other strategies\n      }\n    }\n\n    return results;\n  }\n}\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;AAIzE;;AAcO,MAAM;IACH,iBAAmC;IACnC,qBAA2C;IAEnD,YACE,gBAAkC,EAClC,oBAA0C,CAC1C;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,oBAAoB,GAAG;IAC9B;IAEA;;GAEC,GACD,eACE,WAAmB,EACnB,gBAAwB,EACxB,SAAe,EACf,UAAgB,IAAI,MAAM,EACN;QACpB,kBAAkB;QAClB,IAAI,oBAAoB,GAAG;YACzB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QAChD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,MAAM,IAAI,MAAM,WAAW,KAAK;QAClC;QAEA,OAAO;YACL,IAAI,CAAA,GAAA,8HAAA,CAAA,aAAU,AAAD;YACb;YACA;YACA;YACA;YACA,WAAW,IAAI;QACjB;IACF;IAEA;;GAEC,GACD,MAAM,0BAA0B,QAA4B,EAA6B;QACvF,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAc,8BACZ,gBAAwB,EACxB,SAAe,EACf,OAAa,EAMX;QACF,MAAM,UAKD,CAAC;QAEN,uDAAuD;QACvD,MAAM,aAAa;YAAC;YAAS;YAAa;SAAU;QAEpD,gEAAgE;QAChE,KAAK,MAAM,aAAa,WAAY;YAClC,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,qBAAqB,CAAC;gBAC9D,OAAO,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAC5E,WACA,kBACA,WACA;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,SAAS,CAAC,EAAE;gBACzD,mDAAmD;gBACnD,OAAO,CAAC,UAAU,GAAG;oBACnB,cAAc;oBACd,cAAc;oBACd,MAAM;oBACN,gBAAgB;gBAClB;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,yBAAyB,QAA4B,EAA6B;QACtF,IAAI;YACF,uCAAuC;YACvC,MAAM,mBAAmB,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAE9D,wDAAwD;YACxD,MAAM,mBAAmB,MAAM,IAAI,CAAC,6BAA6B,CAC/D,SAAS,gBAAgB,EACzB,SAAS,SAAS,EAClB,SAAS,OAAO;YAGlB,OAAO;gBACL,YAAY;gBACZ,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,6BACJ,WAAmB,EACnB,UAAkB,EAClB,SAAe,EACf,OAAa,EACb,aAAuB;QAAC;QAAK;QAAM;QAAG;QAAM;QAAK;KAAE,CAAC,cAAc;IAAf,EAIjD;QACF,MAAM,UAA+D,EAAE;QAEvE,KAAK,MAAM,cAAc,WAAY;YACnC,MAAM,SAAS,aAAa;YAC5B,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,aAAa,QAAQ,WAAW;YAErE,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,yBAAyB,CAAC;gBACpD,QAAQ,IAAI,CAAC;oBAAE;oBAAQ;gBAAO;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EAAE;YACzD,8BAA8B;YAChC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,2BACJ,WAAmB,EACnB,gBAAwB,EACxB,aAAmB,EACnB,UAAoD;QAClD;YAAE,OAAO;YAAY,QAAQ;QAAE;QAC/B;YAAE,OAAO;YAAU,QAAQ;QAAG;QAC9B;YAAE,OAAO;YAAW,QAAQ;QAAG;QAC/B;YAAE,OAAO;YAAW,QAAQ;QAAG;QAC/B;YAAE,OAAO;YAAW,QAAQ;QAAG;KAChC,EAMC;QACF,MAAM,UAKD,EAAE;QAEP,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,YAAY,IAAI,KAAK;YAC3B,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,KAAK,OAAO,MAAM;YAEnD,mCAAmC;YACnC,IAAI,UAAU,IAAI,QAAQ;gBACxB;YACF;YAEA,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,aAAa,kBAAkB,WAAW;YAE/E,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,yBAAyB,CAAC;gBACpD,QAAQ,IAAI,CAAC;oBACX,QAAQ,OAAO,KAAK;oBACpB;oBACA;oBACA;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,EAAE;YAC/D,8BAA8B;YAChC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBACJ,WAAmB,EACnB,aAAqB,EACrB,SAAe,EACf,OAAa,EAeZ;QACD,MAAM,eAOD,EAAE;QAEP,IAAI,gBAAgB;QACpB,IAAI,aAAa;QACjB,MAAM,cAAc,IAAI,KAAK;QAE7B,gCAAgC;QAChC,MAAO,eAAe,QAAS;YAC7B,IAAI;gBACF,gDAAgD;gBAChD,MAAM,YAAY,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAC1D,aACA,OACA;gBAGF,IAAI,WAAW;oBACb,MAAM,QAAQ,gBAAgB,UAAU,KAAK;oBAC7C,cAAc;oBACd,iBAAiB;oBAEjB,aAAa,IAAI,CAAC;wBAChB,MAAM,IAAI,KAAK;wBACf,QAAQ;wBACR,OAAO,UAAU,KAAK;wBACtB;wBACA,iBAAiB;wBACjB,sBAAsB;oBACxB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC3D,2BAA2B;YAC7B;YAEA,qBAAqB;YACrB,YAAY,QAAQ,CAAC,YAAY,QAAQ,KAAK;QAChD;QAEA,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,+CAA+C;QAC/C,IAAI;YACF,MAAM,mBAAmB,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAClE,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,EACzB,aACA;YAGF,MAAM,eAAe,aAAa,iBAAiB,YAAY;YAC/D,MAAM,cAAc,eAAe;YACnC,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;YACpD,MAAM,OAAO,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,cAAc;YACxD,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE,eAAe;YAE9D,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAChH;IACF;IAEA;;GAEC,GACD,MAAM,uBACJ,WAAmB,EACnB,WAAmB,EACnB,SAAe,EACf,OAAa,EACb,aAGK;QACH;YAAE,MAAM;YAAqB,MAAM;QAAW;QAC9C;YAAE,MAAM;YAAe,MAAM;QAAc;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAgB;KAChD,EAOC;QACF,MAAM,UAMD,EAAE;QAEP,KAAK,MAAM,YAAY,WAAY;YACjC,IAAI;gBACF,IAAI;gBAEJ,OAAQ,SAAS,IAAI;oBACnB,KAAK;wBACH,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,aAAa,aAAa,WAAW;wBAC1E,MAAM,gBAAgB,MAAM,IAAI,CAAC,yBAAyB,CAAC;wBAC3D,SAAS;4BACP,UAAU,SAAS,IAAI;4BACvB,eAAe,cAAc,YAAY;4BACzC,cAAc,cAAc,YAAY;4BACxC,MAAM,cAAc,IAAI;4BACxB,gBAAgB,cAAc,cAAc;wBAC9C;wBACA;oBAEF,KAAK;wBACH,MAAM,SAAS,KAAK,IAAI,CAAC,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW,WAAW;wBAC1E,MAAM,gBAAgB,cAAc;wBACpC,MAAM,mBAAmB,MAAM,IAAI,CAAC,mBAAmB,CACrD,aACA,eACA,WACA;wBAEF,SAAS;4BACP,UAAU,SAAS,IAAI;4BACvB,eAAe,iBAAiB,aAAa;4BAC7C,cAAc,iBAAiB,YAAY;4BAC3C,MAAM,iBAAiB,IAAI;4BAC3B,gBAAgB,iBAAiB,cAAc;wBACjD;wBACA;oBAEF,KAAK;wBACH,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW,WAAW;wBAC5E,MAAM,kBAAkB,cAAc;wBACtC,uDAAuD;wBACvD,MAAM,qBAAqB,MAAM,IAAI,CAAC,mBAAmB,CACvD,aACA,iBACA,WACA;wBAEF,SAAS;4BACP,UAAU,SAAS,IAAI;4BACvB,eAAe,mBAAmB,aAAa;4BAC/C,cAAc,mBAAmB,YAAY;4BAC7C,MAAM,mBAAmB,IAAI;4BAC7B,gBAAgB,mBAAmB,cAAc;wBACnD;wBACA;oBAEF;wBACE;gBACJ;gBAEA,QAAQ,IAAI,CAAC;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE;YACrD,iCAAiC;YACnC;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/services/comparisonService.ts"], "sourcesContent": ["// Comparison service for the What If investment analysis tool\n\nimport { InvestmentCalculator } from './investmentCalculator';\nimport { BenchmarkDataService } from './benchmarkData';\nimport { formatCurrency, formatPercentage, calculateYearsBetweenDates } from '../utils';\nimport {\n  InvestmentScenario,\n  InvestmentResult,\n  ComparisonResult,\n  ChartDataPoint,\n} from '../types';\n\nexport interface ComparisonSummary {\n  investment: {\n    name: string;\n    initialValue: number;\n    currentValue: number;\n    totalReturn: number;\n    cagr: number;\n    absoluteReturn: number;\n    rank: number;\n  };\n  benchmarks: Array<{\n    name: string;\n    type: string;\n    initialValue: number;\n    currentValue: number;\n    totalReturn: number;\n    cagr: number;\n    absoluteReturn: number;\n    rank: number;\n    outperformance: number; // How much better/worse than investment\n  }>;\n  insights: string[];\n}\n\nexport interface PerformanceMetrics {\n  bestPerformer: {\n    name: string;\n    cagr: number;\n    absoluteReturn: number;\n  };\n  worstPerformer: {\n    name: string;\n    cagr: number;\n    absoluteReturn: number;\n  };\n  averageCagr: number;\n  volatilityRanking: Array<{\n    name: string;\n    volatility: number;\n  }>;\n}\n\nexport class ComparisonService {\n  private investmentCalculator: InvestmentCalculator;\n  private benchmarkDataService: BenchmarkDataService;\n\n  constructor(\n    investmentCalculator: InvestmentCalculator,\n    benchmarkDataService: BenchmarkDataService\n  ) {\n    this.investmentCalculator = investmentCalculator;\n    this.benchmarkDataService = benchmarkDataService;\n  }\n\n  /**\n   * Generate comprehensive comparison summary\n   */\n  async generateComparisonSummary(scenario: InvestmentScenario): Promise<ComparisonSummary> {\n    try {\n      const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);\n      \n      // Prepare investment data\n      const investment = {\n        name: `${scenario.stockSymbol} Investment`,\n        initialValue: comparisonResult.investment.initialValue,\n        currentValue: comparisonResult.investment.currentValue,\n        totalReturn: comparisonResult.investment.totalReturn,\n        cagr: comparisonResult.investment.cagr,\n        absoluteReturn: comparisonResult.investment.absoluteReturn,\n        rank: 0, // Will be calculated below\n      };\n\n      // Prepare benchmark data\n      const benchmarks = Object.entries(comparisonResult.benchmarks).map(([type, data]) => ({\n        name: this.getBenchmarkDisplayName(type),\n        type,\n        initialValue: data.initialValue,\n        currentValue: data.currentValue,\n        totalReturn: data.currentValue - data.initialValue,\n        cagr: data.cagr,\n        absoluteReturn: data.absoluteReturn,\n        rank: 0, // Will be calculated below\n        outperformance: investment.cagr - data.cagr,\n      }));\n\n      // Calculate rankings based on CAGR\n      const allInvestments = [investment, ...benchmarks];\n      allInvestments.sort((a, b) => b.cagr - a.cagr);\n      allInvestments.forEach((item, index) => {\n        item.rank = index + 1;\n      });\n\n      // Generate insights\n      const insights = this.generateInsights(investment, benchmarks, scenario);\n\n      return {\n        investment,\n        benchmarks,\n        insights,\n      };\n    } catch (error) {\n      console.error('Error generating comparison summary:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate performance metrics across all investments\n   */\n  calculatePerformanceMetrics(comparisonSummary: ComparisonSummary): PerformanceMetrics {\n    const allInvestments = [comparisonSummary.investment, ...comparisonSummary.benchmarks];\n    \n    // Find best and worst performers\n    const sortedByCagr = [...allInvestments].sort((a, b) => b.cagr - a.cagr);\n    const bestPerformer = {\n      name: sortedByCagr[0].name,\n      cagr: sortedByCagr[0].cagr,\n      absoluteReturn: sortedByCagr[0].absoluteReturn,\n    };\n    const worstPerformer = {\n      name: sortedByCagr[sortedByCagr.length - 1].name,\n      cagr: sortedByCagr[sortedByCagr.length - 1].cagr,\n      absoluteReturn: sortedByCagr[sortedByCagr.length - 1].absoluteReturn,\n    };\n\n    // Calculate average CAGR\n    const averageCagr = allInvestments.reduce((sum, inv) => sum + inv.cagr, 0) / allInvestments.length;\n\n    // Calculate volatility ranking (simplified - based on absolute return variance from CAGR)\n    const volatilityRanking = allInvestments.map(inv => ({\n      name: inv.name,\n      volatility: Math.abs(inv.absoluteReturn - inv.cagr), // Simplified volatility measure\n    })).sort((a, b) => a.volatility - b.volatility);\n\n    return {\n      bestPerformer,\n      worstPerformer,\n      averageCagr: Number(averageCagr.toFixed(2)),\n      volatilityRanking,\n    };\n  }\n\n  /**\n   * Generate chart data for comparison visualization\n   */\n  async generateComparisonChartData(scenario: InvestmentScenario): Promise<{\n    timeSeriesData: Array<{\n      date: string;\n      investment: number;\n      gold: number;\n      fd: number;\n      nifty: number;\n    }>;\n    barChartData: Array<{\n      name: string;\n      cagr: number;\n      absoluteReturn: number;\n      currentValue: number;\n    }>;\n  }> {\n    try {\n      const comparisonResult = await this.investmentCalculator.calculateWithComparisons(scenario);\n      \n      // For time series, we'll need to get historical data for all benchmarks\n      // This is a simplified version - in production, you'd want actual time series data\n      const timeSeriesData = await this.generateTimeSeriesData(scenario, comparisonResult);\n      \n      // Bar chart data for final comparison\n      const barChartData = [\n        {\n          name: scenario.stockSymbol,\n          cagr: comparisonResult.investment.cagr,\n          absoluteReturn: comparisonResult.investment.absoluteReturn,\n          currentValue: comparisonResult.investment.currentValue,\n        },\n        ...Object.entries(comparisonResult.benchmarks).map(([type, data]) => ({\n          name: this.getBenchmarkDisplayName(type),\n          cagr: data.cagr,\n          absoluteReturn: data.absoluteReturn,\n          currentValue: data.currentValue,\n        })),\n      ];\n\n      return {\n        timeSeriesData,\n        barChartData,\n      };\n    } catch (error) {\n      console.error('Error generating chart data:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Compare multiple stocks against benchmarks\n   */\n  async compareMultipleStocks(\n    scenarios: InvestmentScenario[]\n  ): Promise<Array<{\n    scenario: InvestmentScenario;\n    summary: ComparisonSummary;\n  }>> {\n    const results: Array<{\n      scenario: InvestmentScenario;\n      summary: ComparisonSummary;\n    }> = [];\n\n    for (const scenario of scenarios) {\n      try {\n        const summary = await this.generateComparisonSummary(scenario);\n        results.push({ scenario, summary });\n      } catch (error) {\n        console.error(`Error comparing scenario ${scenario.id}:`, error);\n        // Continue with other scenarios\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * Generate insights based on comparison results\n   */\n  private generateInsights(\n    investment: any,\n    benchmarks: any[],\n    scenario: InvestmentScenario\n  ): string[] {\n    const insights: string[] = [];\n    const years = calculateYearsBetweenDates(scenario.startDate, scenario.endDate);\n\n    // Performance insights\n    if (investment.rank === 1) {\n      insights.push(`🎉 Your ${scenario.stockSymbol} investment outperformed all benchmarks with a CAGR of ${formatPercentage(investment.cagr)}.`);\n    } else {\n      const betterBenchmarks = benchmarks.filter(b => b.rank < investment.rank);\n      if (betterBenchmarks.length > 0) {\n        const bestBenchmark = betterBenchmarks[0];\n        insights.push(`📊 Your investment ranked #${investment.rank}. ${bestBenchmark.name} performed better with ${formatPercentage(bestBenchmark.cagr)} CAGR.`);\n      }\n    }\n\n    // Market comparison insights (updated for real indices)\n    const niftyBenchmark = benchmarks.find(b => b.type === 'NIFTY');\n    const bankNiftyBenchmark = benchmarks.find(b => b.type === 'BANKNIFTY');\n    const niftyITBenchmark = benchmarks.find(b => b.type === 'NIFTYIT');\n\n    if (niftyBenchmark) {\n      if (investment.cagr > niftyBenchmark.cagr) {\n        insights.push(`📈 Your stock selection beat the market (Nifty 50) by ${formatPercentage(investment.cagr - niftyBenchmark.cagr)} annually.`);\n      } else {\n        insights.push(`📉 The market (Nifty 50) outperformed your stock by ${formatPercentage(niftyBenchmark.cagr - investment.cagr)} annually.`);\n      }\n    }\n\n    if (bankNiftyBenchmark) {\n      if (investment.cagr > bankNiftyBenchmark.cagr) {\n        insights.push(`🏦 Your investment outperformed the banking sector (Bank Nifty) by ${formatPercentage(investment.cagr - bankNiftyBenchmark.cagr)} annually.`);\n      } else {\n        insights.push(`🏦 The banking sector (Bank Nifty) outperformed your stock by ${formatPercentage(bankNiftyBenchmark.cagr - investment.cagr)} annually.`);\n      }\n    }\n\n    if (niftyITBenchmark) {\n      if (investment.cagr > niftyITBenchmark.cagr) {\n        insights.push(`💻 Your investment beat the IT sector (Nifty IT) by ${formatPercentage(investment.cagr - niftyITBenchmark.cagr)} annually.`);\n      } else {\n        insights.push(`💻 The IT sector (Nifty IT) outperformed your stock by ${formatPercentage(niftyITBenchmark.cagr - investment.cagr)} annually.`);\n      }\n    }\n\n    // Time-based insights\n    if (years >= 5) {\n      insights.push(`⏰ Over ${Math.round(years)} years, your ${formatCurrency(scenario.investmentAmount)} investment grew to ${formatCurrency(investment.currentValue)}.`);\n    } else if (years >= 1) {\n      insights.push(`📅 In ${Math.round(years * 12)} months, your investment generated ${formatCurrency(investment.totalReturn)} in returns.`);\n    }\n\n    // Value insights\n    if (investment.absoluteReturn > 100) {\n      insights.push(`💰 Your investment more than doubled your money with ${formatPercentage(investment.absoluteReturn)} total returns.`);\n    } else if (investment.absoluteReturn > 50) {\n      insights.push(`💵 Your investment generated strong returns of ${formatPercentage(investment.absoluteReturn)}.`);\n    } else if (investment.absoluteReturn < 0) {\n      insights.push(`⚠️ Your investment resulted in a loss of ${formatPercentage(Math.abs(investment.absoluteReturn))}.`);\n    }\n\n    return insights;\n  }\n\n  /**\n   * Get display name for benchmark type (updated for real market indices)\n   */\n  private getBenchmarkDisplayName(type: string): string {\n    const names: { [key: string]: string } = {\n      'NIFTY': 'Nifty 50',\n      'BANKNIFTY': 'Bank Nifty',\n      'NIFTYIT': 'Nifty IT',\n      'SENSEX': 'BSE Sensex',\n      'NIFTYNEXT50': 'Nifty Next 50',\n    };\n    return names[type] || type;\n  }\n\n  /**\n   * Generate time series data for visualization\n   */\n  private async generateTimeSeriesData(\n    scenario: InvestmentScenario,\n    comparisonResult: ComparisonResult\n  ): Promise<Array<{\n    date: string;\n    investment: number;\n    gold: number;\n    fd: number;\n    nifty: number;\n  }>> {\n    // This is a simplified implementation\n    // In production, you'd fetch actual historical data for all assets\n    const data: Array<{\n      date: string;\n      investment: number;\n      gold: number;\n      fd: number;\n      nifty: number;\n    }> = [];\n\n    const startDate = new Date(scenario.startDate);\n    const endDate = new Date(scenario.endDate);\n    const currentDate = new Date(startDate);\n\n    // Calculate daily growth rates\n    const years = calculateYearsBetweenDates(startDate, endDate);\n    const days = years * 365;\n\n    const investmentDailyGrowth = Math.pow(comparisonResult.investment.currentValue / comparisonResult.investment.initialValue, 1 / days);\n    const goldDailyGrowth = Math.pow(comparisonResult.benchmarks.GOLD.currentValue / comparisonResult.benchmarks.GOLD.initialValue, 1 / days);\n    const fdDailyGrowth = Math.pow(comparisonResult.benchmarks.FD.currentValue / comparisonResult.benchmarks.FD.initialValue, 1 / days);\n    const niftyDailyGrowth = Math.pow(comparisonResult.benchmarks.NIFTY.currentValue / comparisonResult.benchmarks.NIFTY.initialValue, 1 / days);\n\n    let dayCount = 0;\n    while (currentDate <= endDate) {\n      const investmentValue = scenario.investmentAmount * Math.pow(investmentDailyGrowth, dayCount);\n      const goldValue = scenario.investmentAmount * Math.pow(goldDailyGrowth, dayCount);\n      const fdValue = scenario.investmentAmount * Math.pow(fdDailyGrowth, dayCount);\n      const niftyValue = scenario.investmentAmount * Math.pow(niftyDailyGrowth, dayCount);\n\n      data.push({\n        date: currentDate.toISOString().split('T')[0],\n        investment: Math.round(investmentValue),\n        gold: Math.round(goldValue),\n        fd: Math.round(fdValue),\n        nifty: Math.round(niftyValue),\n      });\n\n      currentDate.setDate(currentDate.getDate() + 7); // Weekly data points\n      dayCount += 7;\n    }\n\n    return data;\n  }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AAI9D;;AAkDO,MAAM;IACH,qBAA2C;IAC3C,qBAA2C;IAEnD,YACE,oBAA0C,EAC1C,oBAA0C,CAC1C;QACA,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,oBAAoB,GAAG;IAC9B;IAEA;;GAEC,GACD,MAAM,0BAA0B,QAA4B,EAA8B;QACxF,IAAI;YACF,MAAM,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC;YAElF,0BAA0B;YAC1B,MAAM,aAAa;gBACjB,MAAM,GAAG,SAAS,WAAW,CAAC,WAAW,CAAC;gBAC1C,cAAc,iBAAiB,UAAU,CAAC,YAAY;gBACtD,cAAc,iBAAiB,UAAU,CAAC,YAAY;gBACtD,aAAa,iBAAiB,UAAU,CAAC,WAAW;gBACpD,MAAM,iBAAiB,UAAU,CAAC,IAAI;gBACtC,gBAAgB,iBAAiB,UAAU,CAAC,cAAc;gBAC1D,MAAM;YACR;YAEA,yBAAyB;YACzB,MAAM,aAAa,OAAO,OAAO,CAAC,iBAAiB,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;oBACpF,MAAM,IAAI,CAAC,uBAAuB,CAAC;oBACnC;oBACA,cAAc,KAAK,YAAY;oBAC/B,cAAc,KAAK,YAAY;oBAC/B,aAAa,KAAK,YAAY,GAAG,KAAK,YAAY;oBAClD,MAAM,KAAK,IAAI;oBACf,gBAAgB,KAAK,cAAc;oBACnC,MAAM;oBACN,gBAAgB,WAAW,IAAI,GAAG,KAAK,IAAI;gBAC7C,CAAC;YAED,mCAAmC;YACnC,MAAM,iBAAiB;gBAAC;mBAAe;aAAW;YAClD,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;YAC7C,eAAe,OAAO,CAAC,CAAC,MAAM;gBAC5B,KAAK,IAAI,GAAG,QAAQ;YACtB;YAEA,oBAAoB;YACpB,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,YAAY,YAAY;YAE/D,OAAO;gBACL;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,4BAA4B,iBAAoC,EAAsB;QACpF,MAAM,iBAAiB;YAAC,kBAAkB,UAAU;eAAK,kBAAkB,UAAU;SAAC;QAEtF,iCAAiC;QACjC,MAAM,eAAe;eAAI;SAAe,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;QACvE,MAAM,gBAAgB;YACpB,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI;YAC1B,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI;YAC1B,gBAAgB,YAAY,CAAC,EAAE,CAAC,cAAc;QAChD;QACA,MAAM,iBAAiB;YACrB,MAAM,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI;YAChD,MAAM,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI;YAChD,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,cAAc;QACtE;QAEA,yBAAyB;QACzB,MAAM,cAAc,eAAe,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,IAAI,EAAE,KAAK,eAAe,MAAM;QAElG,0FAA0F;QAC1F,MAAM,oBAAoB,eAAe,GAAG,CAAC,CAAA,MAAO,CAAC;gBACnD,MAAM,IAAI,IAAI;gBACd,YAAY,KAAK,GAAG,CAAC,IAAI,cAAc,GAAG,IAAI,IAAI;YACpD,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;QAE9C,OAAO;YACL;YACA;YACA,aAAa,OAAO,YAAY,OAAO,CAAC;YACxC;QACF;IACF;IAEA;;GAEC,GACD,MAAM,4BAA4B,QAA4B,EAc3D;QACD,IAAI;YACF,MAAM,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC;YAElF,wEAAwE;YACxE,mFAAmF;YACnF,MAAM,iBAAiB,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU;YAEnE,sCAAsC;YACtC,MAAM,eAAe;gBACnB;oBACE,MAAM,SAAS,WAAW;oBAC1B,MAAM,iBAAiB,UAAU,CAAC,IAAI;oBACtC,gBAAgB,iBAAiB,UAAU,CAAC,cAAc;oBAC1D,cAAc,iBAAiB,UAAU,CAAC,YAAY;gBACxD;mBACG,OAAO,OAAO,CAAC,iBAAiB,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;wBACpE,MAAM,IAAI,CAAC,uBAAuB,CAAC;wBACnC,MAAM,KAAK,IAAI;wBACf,gBAAgB,KAAK,cAAc;wBACnC,cAAc,KAAK,YAAY;oBACjC,CAAC;aACF;YAED,OAAO;gBACL;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBACJ,SAA+B,EAI7B;QACF,MAAM,UAGD,EAAE;QAEP,KAAK,MAAM,YAAY,UAAW;YAChC,IAAI;gBACF,MAAM,UAAU,MAAM,IAAI,CAAC,yBAAyB,CAAC;gBACrD,QAAQ,IAAI,CAAC;oBAAE;oBAAU;gBAAQ;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;YAC1D,gCAAgC;YAClC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,iBACN,UAAe,EACf,UAAiB,EACjB,QAA4B,EAClB;QACV,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,SAAS,EAAE,SAAS,OAAO;QAE7E,uBAAuB;QACvB,IAAI,WAAW,IAAI,KAAK,GAAG;YACzB,SAAS,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,WAAW,CAAC,uDAAuD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;QAC7I,OAAO;YACL,MAAM,mBAAmB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,WAAW,IAAI;YACxE,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,MAAM,gBAAgB,gBAAgB,CAAC,EAAE;gBACzC,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,cAAc,IAAI,CAAC,uBAAuB,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc,IAAI,EAAE,MAAM,CAAC;YAC1J;QACF;QAEA,wDAAwD;QACxD,MAAM,iBAAiB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACvD,MAAM,qBAAqB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC3D,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAEzD,IAAI,gBAAgB;YAClB,IAAI,WAAW,IAAI,GAAG,eAAe,IAAI,EAAE;gBACzC,SAAS,IAAI,CAAC,CAAC,sDAAsD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,GAAG,eAAe,IAAI,EAAE,UAAU,CAAC;YAC5I,OAAO;gBACL,SAAS,IAAI,CAAC,CAAC,oDAAoD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,IAAI,GAAG,WAAW,IAAI,EAAE,UAAU,CAAC;YAC1I;QACF;QAEA,IAAI,oBAAoB;YACtB,IAAI,WAAW,IAAI,GAAG,mBAAmB,IAAI,EAAE;gBAC7C,SAAS,IAAI,CAAC,CAAC,mEAAmE,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,GAAG,mBAAmB,IAAI,EAAE,UAAU,CAAC;YAC7J,OAAO;gBACL,SAAS,IAAI,CAAC,CAAC,8DAA8D,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,mBAAmB,IAAI,GAAG,WAAW,IAAI,EAAE,UAAU,CAAC;YACxJ;QACF;QAEA,IAAI,kBAAkB;YACpB,IAAI,WAAW,IAAI,GAAG,iBAAiB,IAAI,EAAE;gBAC3C,SAAS,IAAI,CAAC,CAAC,oDAAoD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,GAAG,iBAAiB,IAAI,EAAE,UAAU,CAAC;YAC5I,OAAO;gBACL,SAAS,IAAI,CAAC,CAAC,uDAAuD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,IAAI,GAAG,WAAW,IAAI,EAAE,UAAU,CAAC;YAC/I;QACF;QAEA,sBAAsB;QACtB,IAAI,SAAS,GAAG;YACd,SAAS,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,OAAO,aAAa,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,gBAAgB,EAAE,oBAAoB,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,YAAY,EAAE,CAAC,CAAC;QACrK,OAAO,IAAI,SAAS,GAAG;YACrB,SAAS,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,QAAQ,IAAI,mCAAmC,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,WAAW,EAAE,YAAY,CAAC;QACzI;QAEA,iBAAiB;QACjB,IAAI,WAAW,cAAc,GAAG,KAAK;YACnC,SAAS,IAAI,CAAC,CAAC,qDAAqD,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,cAAc,EAAE,eAAe,CAAC;QACpI,OAAO,IAAI,WAAW,cAAc,GAAG,IAAI;YACzC,SAAS,IAAI,CAAC,CAAC,+CAA+C,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,cAAc,EAAE,CAAC,CAAC;QAChH,OAAO,IAAI,WAAW,cAAc,GAAG,GAAG;YACxC,SAAS,IAAI,CAAC,CAAC,yCAAyC,EAAE,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,GAAG,CAAC,WAAW,cAAc,GAAG,CAAC,CAAC;QACpH;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,wBAAwB,IAAY,EAAU;QACpD,MAAM,QAAmC;YACvC,SAAS;YACT,aAAa;YACb,WAAW;YACX,UAAU;YACV,eAAe;QACjB;QACA,OAAO,KAAK,CAAC,KAAK,IAAI;IACxB;IAEA;;GAEC,GACD,MAAc,uBACZ,QAA4B,EAC5B,gBAAkC,EAOhC;QACF,sCAAsC;QACtC,mEAAmE;QACnE,MAAM,OAMD,EAAE;QAEP,MAAM,YAAY,IAAI,KAAK,SAAS,SAAS;QAC7C,MAAM,UAAU,IAAI,KAAK,SAAS,OAAO;QACzC,MAAM,cAAc,IAAI,KAAK;QAE7B,+BAA+B;QAC/B,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;QACpD,MAAM,OAAO,QAAQ;QAErB,MAAM,wBAAwB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,YAAY,EAAE,IAAI;QAChI,MAAM,kBAAkB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI;QACpI,MAAM,gBAAgB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,EAAE,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI;QAC9H,MAAM,mBAAmB,KAAK,GAAG,CAAC,iBAAiB,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,iBAAiB,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI;QAEvI,IAAI,WAAW;QACf,MAAO,eAAe,QAAS;YAC7B,MAAM,kBAAkB,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,uBAAuB;YACpF,MAAM,YAAY,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,iBAAiB;YACxE,MAAM,UAAU,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,eAAe;YACpE,MAAM,aAAa,SAAS,gBAAgB,GAAG,KAAK,GAAG,CAAC,kBAAkB;YAE1E,KAAK,IAAI,CAAC;gBACR,MAAM,YAAY,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC7C,YAAY,KAAK,KAAK,CAAC;gBACvB,MAAM,KAAK,KAAK,CAAC;gBACjB,IAAI,KAAK,KAAK,CAAC;gBACf,OAAO,KAAK,KAAK,CAAC;YACpB;YAEA,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK,IAAI,qBAAqB;YACrE,YAAY;QACd;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1979, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/app/api/analyze/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { AngelOneClient } from '@/lib/api/angelone';\nimport { StockDataService } from '@/lib/services/stockData';\nimport { BenchmarkDataService } from '@/lib/services/benchmarkData';\nimport { InvestmentCalculator } from '@/lib/services/investmentCalculator';\nimport { ComparisonService } from '@/lib/services/comparisonService';\nimport { InvestmentScenario } from '@/lib/types';\nimport { generateId } from '@/lib/utils';\n\n// Initialize services\nlet angelOneClient: AngelOneClient;\nlet stockDataService: StockDataService;\nlet benchmarkDataService: BenchmarkDataService;\nlet investmentCalculator: InvestmentCalculator;\nlet comparisonService: ComparisonService;\n\nasync function initializeServices() {\n  if (!angelOneClient) {\n    console.log('🔧 Creating Angel One client...');\n\n    // Check environment variables\n    const requiredEnvVars = {\n      apiKey: process.env.ANGEL_ONE_API_KEY,\n      clientId: process.env.ANGEL_ONE_CLIENT_ID,\n      password: process.env.ANGEL_ONE_PASSWORD,\n      totpSecret: process.env.ANGEL_ONE_TOTP_SECRET,\n    };\n\n    console.log('🔍 Environment variables check:', {\n      apiKey: requiredEnvVars.apiKey ? 'SET' : 'MISSING',\n      clientId: requiredEnvVars.clientId ? 'SET' : 'MISSING',\n      password: requiredEnvVars.password ? 'SET' : 'MISSING',\n      totpSecret: requiredEnvVars.totpSecret ? 'SET' : 'MISSING',\n    });\n\n    // Initialize Angel One client with environment variables\n    angelOneClient = new AngelOneClient({\n      apiKey: requiredEnvVars.apiKey!,\n      clientId: requiredEnvVars.clientId!,\n      password: requiredEnvVars.password!,\n      totpSecret: requiredEnvVars.totpSecret!,\n    });\n\n    console.log('🔐 Attempting Angel One authentication...');\n\n    // Authenticate\n    const loginResult = await angelOneClient.login();\n    console.log('🔐 Login result:', loginResult);\n\n    if (!loginResult.success) {\n      throw new Error(`Angel One authentication failed: ${loginResult.message}`);\n    }\n\n    console.log('✅ Angel One authentication successful');\n\n    // Initialize services\n    console.log('🔧 Initializing services...');\n    stockDataService = new StockDataService(angelOneClient);\n    benchmarkDataService = new BenchmarkDataService(angelOneClient);\n    investmentCalculator = new InvestmentCalculator(stockDataService, benchmarkDataService);\n    comparisonService = new ComparisonService(investmentCalculator, benchmarkDataService);\n\n    console.log('✅ All services initialized successfully');\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🚀 Starting investment analysis...');\n\n    // Initialize services if not already done\n    console.log('🔧 Initializing services...');\n    await initializeServices();\n    console.log('✅ Services initialized successfully');\n\n    // Parse request body\n    console.log('📥 Parsing request body...');\n    const body = await request.json();\n    const { stockSymbol, investmentAmount, startDate, endDate } = body;\n\n    console.log('📊 Request data:', {\n      stockSymbol,\n      investmentAmount,\n      startDate,\n      endDate\n    });\n\n    // Validate input\n    if (!stockSymbol || !investmentAmount || !startDate || !endDate) {\n      console.log('❌ Validation failed: Missing required fields');\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      );\n    }\n\n    // Create investment scenario\n    const scenario: InvestmentScenario = {\n      id: generateId(),\n      stockSymbol,\n      investmentAmount: parseFloat(investmentAmount),\n      startDate: new Date(startDate),\n      endDate: new Date(endDate),\n      createdAt: new Date(),\n    };\n\n    // Validate date range\n    if (scenario.startDate >= scenario.endDate) {\n      return NextResponse.json(\n        { error: 'Start date must be before end date' },\n        { status: 400 }\n      );\n    }\n\n    if (scenario.startDate > new Date()) {\n      return NextResponse.json(\n        { error: 'Start date cannot be in the future' },\n        { status: 400 }\n      );\n    }\n\n    // Calculate investment result\n    console.log('📊 Calculating investment result...');\n    const investmentResult = await investmentCalculator.calculateInvestmentResult(scenario);\n    console.log('✅ Investment result calculated');\n\n    // Calculate comparison with benchmarks\n    console.log('📊 Calculating benchmark comparisons...');\n    const comparisonResult = await investmentCalculator.calculateWithComparisons(scenario);\n    console.log('✅ Benchmark comparisons calculated');\n\n    // Generate comparison summary\n    console.log('📊 Generating comparison summary...');\n    const comparisonSummary = await comparisonService.generateComparisonSummary(scenario);\n    console.log('✅ Comparison summary generated');\n\n    // Generate chart data\n    console.log('📊 Generating chart data...');\n    const chartData = await comparisonService.generateComparisonChartData(scenario);\n    console.log('✅ Chart data generated');\n\n    // Return results\n    return NextResponse.json({\n      investmentResult,\n      comparisonResult,\n      comparisonSummary,\n      chartData,\n    });\n\n  } catch (error) {\n    console.error('💥 Analysis API error:', error);\n    console.error('Error type:', error?.constructor?.name);\n    console.error('Error message:', error?.message);\n    console.error('Error stack:', error?.stack);\n\n    // Handle specific error types\n    if (error instanceof Error) {\n      console.log('🔍 Checking error type:', error.message);\n\n      if (error.message.includes('Stock not found') || error.message.includes('Invalid stock symbol')) {\n        console.log('❌ Stock not found error');\n        return NextResponse.json(\n          { error: 'Stock symbol not found. Please check the symbol and try again.' },\n          { status: 404 }\n        );\n      }\n\n      if (error.message.includes('Rate limit')) {\n        console.log('❌ Rate limit error');\n        return NextResponse.json(\n          { error: 'Too many requests. Please wait a moment and try again.' },\n          { status: 429 }\n        );\n      }\n\n      if (error.message.includes('Authentication')) {\n        console.log('❌ Authentication error');\n        return NextResponse.json(\n          { error: 'Authentication failed. Please try again later.' },\n          { status: 401 }\n        );\n      }\n    }\n\n    console.log('❌ Generic error, returning 500');\n    return NextResponse.json(\n      { error: 'An error occurred while analyzing the investment. Please try again.', details: error?.message },\n      { status: 500 }\n    );\n  }\n}\n\n// Health check endpoint\nexport async function GET() {\n  try {\n    await initializeServices();\n    return NextResponse.json({ status: 'OK', message: 'Analysis API is running' });\n  } catch (error) {\n    console.error('Health check failed:', error);\n    return NextResponse.json(\n      { status: 'ERROR', message: 'Service unavailable' },\n      { status: 503 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;AAEA,sBAAsB;AACtB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,eAAe;IACb,IAAI,CAAC,gBAAgB;QACnB,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,kBAAkB;YACtB,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;YACrC,UAAU,QAAQ,GAAG,CAAC,mBAAmB;YACzC,UAAU,QAAQ,GAAG,CAAC,kBAAkB;YACxC,YAAY,QAAQ,GAAG,CAAC,qBAAqB;QAC/C;QAEA,QAAQ,GAAG,CAAC,mCAAmC;YAC7C,QAAQ,gBAAgB,MAAM,GAAG,QAAQ;YACzC,UAAU,gBAAgB,QAAQ,GAAG,QAAQ;YAC7C,UAAU,gBAAgB,QAAQ,GAAG,QAAQ;YAC7C,YAAY,gBAAgB,UAAU,GAAG,QAAQ;QACnD;QAEA,yDAAyD;QACzD,iBAAiB,IAAI,+HAAA,CAAA,iBAAc,CAAC;YAClC,QAAQ,gBAAgB,MAAM;YAC9B,UAAU,gBAAgB,QAAQ;YAClC,UAAU,gBAAgB,QAAQ;YAClC,YAAY,gBAAgB,UAAU;QACxC;QAEA,QAAQ,GAAG,CAAC;QAEZ,eAAe;QACf,MAAM,cAAc,MAAM,eAAe,KAAK;QAC9C,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,YAAY,OAAO,EAAE;QAC3E;QAEA,QAAQ,GAAG,CAAC;QAEZ,sBAAsB;QACtB,QAAQ,GAAG,CAAC;QACZ,mBAAmB,IAAI,qIAAA,CAAA,mBAAgB,CAAC;QACxC,uBAAuB,IAAI,yIAAA,CAAA,uBAAoB,CAAC;QAChD,uBAAuB,IAAI,gJAAA,CAAA,uBAAoB,CAAC,kBAAkB;QAClE,oBAAoB,IAAI,6IAAA,CAAA,oBAAiB,CAAC,sBAAsB;QAEhE,QAAQ,GAAG,CAAC;IACd;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,0CAA0C;QAC1C,QAAQ,GAAG,CAAC;QACZ,MAAM;QACN,QAAQ,GAAG,CAAC;QAEZ,qBAAqB;QACrB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAE9D,QAAQ,GAAG,CAAC,oBAAoB;YAC9B;YACA;YACA;YACA;QACF;QAEA,iBAAiB;QACjB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,aAAa,CAAC,SAAS;YAC/D,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,MAAM,WAA+B;YACnC,IAAI,CAAA,GAAA,8HAAA,CAAA,aAAU,AAAD;YACb;YACA,kBAAkB,WAAW;YAC7B,WAAW,IAAI,KAAK;YACpB,SAAS,IAAI,KAAK;YAClB,WAAW,IAAI;QACjB;QAEA,sBAAsB;QACtB,IAAI,SAAS,SAAS,IAAI,SAAS,OAAO,EAAE;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,SAAS,SAAS,GAAG,IAAI,QAAQ;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,QAAQ,GAAG,CAAC;QACZ,MAAM,mBAAmB,MAAM,qBAAqB,yBAAyB,CAAC;QAC9E,QAAQ,GAAG,CAAC;QAEZ,uCAAuC;QACvC,QAAQ,GAAG,CAAC;QACZ,MAAM,mBAAmB,MAAM,qBAAqB,wBAAwB,CAAC;QAC7E,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,QAAQ,GAAG,CAAC;QACZ,MAAM,oBAAoB,MAAM,kBAAkB,yBAAyB,CAAC;QAC5E,QAAQ,GAAG,CAAC;QAEZ,sBAAsB;QACtB,QAAQ,GAAG,CAAC;QACZ,MAAM,YAAY,MAAM,kBAAkB,2BAA2B,CAAC;QACtE,QAAQ,GAAG,CAAC;QAEZ,iBAAiB;QACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,QAAQ,KAAK,CAAC,eAAe,OAAO,aAAa;QACjD,QAAQ,KAAK,CAAC,kBAAkB,OAAO;QACvC,QAAQ,KAAK,CAAC,gBAAgB,OAAO;QAErC,8BAA8B;QAC9B,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,GAAG,CAAC,2BAA2B,MAAM,OAAO;YAEpD,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,CAAC,QAAQ,CAAC,yBAAyB;gBAC/F,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiE,GAC1E;oBAAE,QAAQ;gBAAI;YAElB;YAEA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,eAAe;gBACxC,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyD,GAClE;oBAAE,QAAQ;gBAAI;YAElB;YAEA,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,mBAAmB;gBAC5C,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiD,GAC1D;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAuE,SAAS,OAAO;QAAQ,GACxG;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,QAAQ;YAAM,SAAS;QAA0B;IAC9E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,QAAQ;YAAS,SAAS;QAAsB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}