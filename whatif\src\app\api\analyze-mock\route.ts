import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Mock analysis API called');
    
    // Parse request body
    const body = await request.json();
    const { stockSymbol, investmentAmount, startDate, endDate } = body;

    console.log('📊 Mock analysis request:', {
      stockSymbol,
      investmentAmount,
      startDate,
      endDate
    });

    // Validate input
    if (!stockSymbol || !investmentAmount || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Mock calculation - simulate TCS investment growth
    const start = new Date(startDate);
    const end = new Date(endDate);
    const years = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
    
    // Mock TCS performance (approximate historical performance)
    const mockCAGR = 15.5; // TCS has historically performed well
    const currentValue = investmentAmount * Math.pow(1 + mockCAGR / 100, years);
    const absoluteReturn = ((currentValue - investmentAmount) / investmentAmount) * 100;

    // Mock investment result
    const investmentResult = {
      scenario: {
        id: `mock-${Date.now()}`,
        stockSymbol,
        investmentAmount: parseFloat(investmentAmount),
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        createdAt: new Date(),
      },
      initialValue: investmentAmount,
      currentValue: Math.round(currentValue),
      absoluteReturn: Math.round(absoluteReturn * 100) / 100,
      cagr: mockCAGR,
      totalReturn: Math.round((currentValue - investmentAmount) * 100) / 100,
      annualizedReturn: mockCAGR,
    };

    // Mock benchmark data
    const comparisonResult = {
      investment: {
        name: `${stockSymbol} Investment`,
        initialValue: investmentAmount,
        currentValue: Math.round(currentValue),
        cagr: mockCAGR,
        absoluteReturn: Math.round(absoluteReturn * 100) / 100,
      },
      benchmarks: [
        {
          name: 'Gold',
          initialValue: investmentAmount,
          currentValue: Math.round(investmentAmount * Math.pow(1.08, years)), // 8% CAGR for gold
          cagr: 8.0,
          absoluteReturn: Math.round(((investmentAmount * Math.pow(1.08, years) - investmentAmount) / investmentAmount) * 10000) / 100,
        },
        {
          name: 'Fixed Deposit',
          initialValue: investmentAmount,
          currentValue: Math.round(investmentAmount * Math.pow(1.065, years)), // 6.5% CAGR for FD
          cagr: 6.5,
          absoluteReturn: Math.round(((investmentAmount * Math.pow(1.065, years) - investmentAmount) / investmentAmount) * 10000) / 100,
        },
        {
          name: 'Nifty 50',
          initialValue: investmentAmount,
          currentValue: Math.round(investmentAmount * Math.pow(1.12, years)), // 12% CAGR for Nifty
          cagr: 12.0,
          absoluteReturn: Math.round(((investmentAmount * Math.pow(1.12, years) - investmentAmount) / investmentAmount) * 10000) / 100,
        },
      ],
    };

    // Mock comparison summary
    const comparisonSummary = {
      investment: {
        name: `${stockSymbol} Investment`,
        cagr: mockCAGR,
        totalReturn: Math.round((currentValue - investmentAmount) * 100) / 100,
      },
      benchmarks: comparisonResult.benchmarks,
      insights: [
        `Your ${stockSymbol} investment outperformed Fixed Deposits by ${(mockCAGR - 6.5).toFixed(1)}% annually`,
        `${stockSymbol} delivered ${mockCAGR}% CAGR over the ${years.toFixed(1)} year period`,
        `Total wealth created: ₹${Math.round((currentValue - investmentAmount)).toLocaleString('en-IN')}`,
        `Investment multiplied by ${(currentValue / investmentAmount).toFixed(2)}x`,
      ],
    };

    // Mock chart data
    const chartData = {
      timeSeriesData: [
        { date: startDate, value: investmentAmount, label: 'Investment Start' },
        { date: endDate, value: Math.round(currentValue), label: 'Current Value' },
      ],
      barChartData: [
        { name: stockSymbol, value: mockCAGR, color: '#3b82f6' },
        { name: 'Nifty 50', value: 12.0, color: '#8b5cf6' },
        { name: 'Gold', value: 8.0, color: '#f59e0b' },
        { name: 'FD', value: 6.5, color: '#10b981' },
      ],
    };

    console.log('✅ Mock analysis completed successfully');

    return NextResponse.json({
      investmentResult,
      comparisonResult,
      comparisonSummary,
      chartData,
    });

  } catch (error) {
    console.error('❌ Mock analysis error:', error);
    return NextResponse.json(
      { 
        error: 'Mock analysis failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ 
    status: 'OK', 
    message: 'Mock Analysis API is running',
    note: 'This is a mock implementation for testing purposes'
  });
}
