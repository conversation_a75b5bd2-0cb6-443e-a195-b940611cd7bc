{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/app/api/test/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('🧪 Test API route called');\n    \n    // Test environment variables\n    const envVars = {\n      ANGEL_ONE_API_KEY: process.env.ANGEL_ONE_API_KEY ? 'SET' : 'MISSING',\n      ANGEL_ONE_CLIENT_ID: process.env.ANGEL_ONE_CLIENT_ID ? 'SET' : 'MISSING',\n      ANGEL_ONE_PASSWORD: process.env.ANGEL_ONE_PASSWORD ? 'SET' : 'MISSING',\n      ANGEL_ONE_TOTP_SECRET: process.env.ANGEL_ONE_TOTP_SECRET ? 'SET' : 'MISSING',\n    };\n\n    console.log('🔧 Environment variables:', envVars);\n\n    return NextResponse.json({\n      success: true,\n      message: 'Test API route working',\n      timestamp: new Date().toISOString(),\n      environment: envVars,\n    });\n  } catch (error) {\n    console.error('❌ Test API route error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error',\n        timestamp: new Date().toISOString(),\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🧪 Test API POST route called');\n    const body = await request.json();\n    console.log('📥 Request body:', body);\n\n    return NextResponse.json({\n      success: true,\n      message: 'Test POST API route working',\n      receivedData: body,\n      timestamp: new Date().toISOString(),\n    });\n  } catch (error) {\n    console.error('❌ Test POST API route error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error',\n        timestamp: new Date().toISOString(),\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,6BAA6B;QAC7B,MAAM,UAAU;YACd,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB,GAAG,QAAQ;YAC3D,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB,GAAG,QAAQ;YAC/D,oBAAoB,QAAQ,GAAG,CAAC,kBAAkB,GAAG,QAAQ;YAC7D,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB,GAAG,QAAQ;QACrE;QAEA,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,WAAW,IAAI,OAAO,WAAW;QACnC,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,cAAc;YACd,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,WAAW,IAAI,OAAO,WAAW;QACnC,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}