// Real API test setup - NO MOCKS, only real Angel One API calls
import { AngelOneClient } from '../api/angelone';
import { StockDataService } from '../services/stockData';
import { BenchmarkDataService } from '../services/benchmarkData';
import { InvestmentCalculator } from '../services/investmentCalculator';
import { ComparisonService } from '../services/comparisonService';

// Real credentials for testing
const REAL_API_CONFIG = {
  apiKey: 'TU9sOEpR',
  clientId: 'M834963',
  password: '3318',
  totpSecret: 'CRAFUYSVQVWTWPHVWZ55KV5VJI',
};

// Global test client instance
let globalClient: AngelOneClient | null = null;
let isLoggedIn = false;

/**
 * Get authenticated Angel One client for testing
 * Reuses the same client across tests to avoid rate limiting
 */
export async function getAuthenticatedClient(): Promise<AngelOneClient> {
  if (!globalClient) {
    globalClient = new AngelOneClient(REAL_API_CONFIG);
  }

  if (!isLoggedIn) {
    const loginResult = await globalClient.login();
    if (!loginResult.success) {
      throw new Error(`Failed to login to Angel One API: ${loginResult.message}`);
    }
    isLoggedIn = true;
    console.log('✅ Authenticated with Angel One API for testing');
  }

  return globalClient;
}

/**
 * Get real stock data service for testing
 */
export async function getRealStockDataService(): Promise<StockDataService> {
  const client = await getAuthenticatedClient();
  return new StockDataService(client);
}

/**
 * Get real benchmark data service for testing
 */
export async function getRealBenchmarkDataService(): Promise<BenchmarkDataService> {
  const client = await getAuthenticatedClient();
  return new BenchmarkDataService(client);
}

/**
 * Get real investment calculator for testing
 */
export async function getRealInvestmentCalculator(): Promise<InvestmentCalculator> {
  const stockService = await getRealStockDataService();
  const benchmarkService = await getRealBenchmarkDataService();
  return new InvestmentCalculator(stockService, benchmarkService);
}

/**
 * Get real comparison service for testing
 */
export async function getRealComparisonService(): Promise<ComparisonService> {
  const calculator = await getRealInvestmentCalculator();
  const benchmarkService = await getRealBenchmarkDataService();
  return new ComparisonService(calculator, benchmarkService);
}

/**
 * Test data constants - real stock symbols and tokens
 */
export const TEST_STOCKS = {
  SBIN: {
    symbol: 'SBIN-EQ',
    token: '3045',
    exchange: 'NSE' as const,
    name: 'State Bank of India',
  },
  RELIANCE: {
    symbol: 'RELIANCE-EQ', 
    token: '2885',
    exchange: 'NSE' as const,
    name: 'Reliance Industries',
  },
  TCS: {
    symbol: 'TCS-EQ',
    token: '11536',
    exchange: 'NSE' as const,
    name: 'Tata Consultancy Services',
  },
} as const;

/**
 * Test date ranges for consistent testing
 */
export const TEST_DATE_RANGES = {
  SHORT_TERM: {
    start: new Date('2024-01-01'),
    end: new Date('2024-01-31'),
    label: '1 Month',
  },
  MEDIUM_TERM: {
    start: new Date('2023-07-01'),
    end: new Date('2024-01-31'),
    label: '6 Months',
  },
  LONG_TERM: {
    start: new Date('2023-01-01'),
    end: new Date('2024-01-31'),
    label: '1 Year',
  },
} as const;

/**
 * Test investment amounts
 */
export const TEST_AMOUNTS = {
  SMALL: 50000,   // ₹50,000
  MEDIUM: 100000, // ₹1,00,000
  LARGE: 500000,  // ₹5,00,000
} as const;

/**
 * Cleanup function to logout after tests
 */
export async function cleanupTestSession(): Promise<void> {
  if (globalClient && isLoggedIn) {
    try {
      await globalClient.logout();
      console.log('✅ Logged out from Angel One API');
    } catch (error) {
      console.warn('⚠️ Logout failed:', error);
    }
    isLoggedIn = false;
  }
}

/**
 * Wait function to respect rate limits
 */
export function waitForRateLimit(ms: number = 1000): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function for API calls that might fail due to network issues
 */
export async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error as Error;
      console.warn(`API call attempt ${attempt} failed:`, error);
      
      if (attempt < maxRetries) {
        await waitForRateLimit(delayMs * attempt); // Exponential backoff
      }
    }
  }
  
  throw lastError!;
}

/**
 * Validate that we have real data (not mock data)
 */
export function validateRealData(data: any, dataType: string): void {
  if (!data) {
    throw new Error(`${dataType} is null or undefined - this suggests mock data`);
  }
  
  if (Array.isArray(data) && data.length === 0) {
    throw new Error(`${dataType} is empty array - this suggests mock data`);
  }
  
  // Check for common mock patterns
  const dataStr = JSON.stringify(data);
  if (dataStr.includes('mock') || dataStr.includes('test-') || dataStr.includes('fake')) {
    throw new Error(`${dataType} contains mock patterns - this is not real data`);
  }
  
  console.log(`✅ Validated real ${dataType} data`);
}
