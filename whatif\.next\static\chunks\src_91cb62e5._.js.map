{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/config/index.ts"], "sourcesContent": ["// Configuration for the What If investment analysis tool\n\nexport const APP_CONFIG = {\n  name: 'What If',\n  description: 'Investment Analysis Tool for Indian Equities',\n  version: '1.0.0',\n  author: 'What If Team',\n} as const;\n\nexport const API_CONFIG = {\n  angelOne: {\n    baseUrl: 'https://apiconnect.angelone.in',\n    version: 'v1',\n    endpoints: {\n      login: '/rest/auth/angelbroking/user/v1/loginByPassword',\n      profile: '/rest/secure/angelbroking/user/v1/getProfile',\n      historicalData: '/rest/secure/angelbroking/historical/v1/getCandleData',\n      ltp: '/rest/secure/angelbroking/order/v1/getLtpData',\n      logout: '/rest/secure/angelbroking/user/v1/logout',\n    },\n  },\n  rateLimit: {\n    requestsPerSecond: 10,\n    requestsPerMinute: 100,\n  },\n} as const;\n\nexport const MARKET_CONFIG = {\n  exchanges: ['NSE', 'BSE'] as const,\n  segments: ['EQUITY'] as const, // Excluding OPTIONS as per requirement\n  tradingHours: {\n    start: '09:15',\n    end: '15:30',\n    timezone: 'Asia/Kolkata',\n  },\n  holidays: [], // To be populated with market holidays\n} as const;\n\nexport const BENCHMARK_CONFIG = {\n  types: {\n    GOLD: {\n      name: 'Gold',\n      symbol: 'GOLD',\n      description: 'Gold prices in INR per 10 grams',\n    },\n    FD: {\n      name: 'Fixed Deposit',\n      symbol: 'FD',\n      description: 'Average FD rates from major banks',\n      defaultRate: 6.5, // Default FD rate percentage\n    },\n    NIFTY: {\n      name: 'Nifty 50',\n      symbol: 'NIFTY',\n      description: 'NSE Nifty 50 Index',\n      token: '********', // Angel One token for Nifty 50\n    },\n  },\n} as const;\n\nexport const CALCULATION_CONFIG = {\n  precision: {\n    currency: 2,\n    percentage: 2,\n    cagr: 2,\n  },\n  defaults: {\n    fdRate: 6.5, // Default FD rate percentage\n    inflationRate: 6.0, // Default inflation rate\n  },\n} as const;\n\nexport const UI_CONFIG = {\n  theme: {\n    primary: '#1f2937',\n    secondary: '#374151',\n    accent: '#3b82f6',\n    success: '#10b981',\n    warning: '#f59e0b',\n    error: '#ef4444',\n  },\n  charts: {\n    defaultHeight: 400,\n    colors: {\n      investment: '#3b82f6',\n      gold: '#f59e0b',\n      fd: '#10b981',\n      nifty: '#8b5cf6',\n    },\n  },\n} as const;\n\nexport const STORAGE_CONFIG = {\n  keys: {\n    userPreferences: 'whatif_user_preferences',\n    savedScenarios: 'whatif_saved_scenarios',\n    apiCredentials: 'whatif_api_credentials',\n  },\n  encryption: {\n    enabled: true,\n    algorithm: 'AES-256-GCM',\n  },\n} as const;\n\n// Environment-specific configuration\nexport const getEnvironmentConfig = () => {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const isProduction = process.env.NODE_ENV === 'production';\n\n  return {\n    isDevelopment,\n    isProduction,\n    apiUrl: process.env.NEXT_PUBLIC_API_URL || API_CONFIG.angelOne.baseUrl,\n    enableLogging: isDevelopment,\n    enableAnalytics: isProduction,\n  };\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;;;;;;AA0GjC;AAxGjB,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;AACV;AAEO,MAAM,aAAa;IACxB,UAAU;QACR,SAAS;QACT,SAAS;QACT,WAAW;YACT,OAAO;YACP,SAAS;YACT,gBAAgB;YAChB,KAAK;YACL,QAAQ;QACV;IACF;IACA,WAAW;QACT,mBAAmB;QACnB,mBAAmB;IACrB;AACF;AAEO,MAAM,gBAAgB;IAC3B,WAAW;QAAC;QAAO;KAAM;IACzB,UAAU;QAAC;KAAS;IACpB,cAAc;QACZ,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IACA,UAAU,EAAE;AACd;AAEO,MAAM,mBAAmB;IAC9B,OAAO;QACL,MAAM;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;QACf;QACA,IAAI;YACF,MAAM;YACN,QAAQ;YACR,aAAa;YACb,aAAa;QACf;QACA,OAAO;YACL,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;IACF;AACF;AAEO,MAAM,qBAAqB;IAChC,WAAW;QACT,UAAU;QACV,YAAY;QACZ,MAAM;IACR;IACA,UAAU;QACR,QAAQ;QACR,eAAe;IACjB;AACF;AAEO,MAAM,YAAY;IACvB,OAAO;QACL,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,SAAS;QACT,OAAO;IACT;IACA,QAAQ;QACN,eAAe;QACf,QAAQ;YACN,YAAY;YACZ,MAAM;YACN,IAAI;YACJ,OAAO;QACT;IACF;AACF;AAEO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;IAClB;IACA,YAAY;QACV,SAAS;QACT,WAAW;IACb;AACF;AAGO,MAAM,uBAAuB;IAClC,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,eAAe,oDAAyB;IAE9C,OAAO;QACL;QACA;QACA,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,WAAW,QAAQ,CAAC,OAAO;QACtE,eAAe;QACf,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/lib/utils/index.ts"], "sourcesContent": ["// Utility functions for the What If investment analysis tool\n\nimport { CALCULATION_CONFIG } from '../config';\n\n/**\n * Calculate Compound Annual Growth Rate (CAGR)\n * @param initialValue - Initial investment value\n * @param finalValue - Final investment value\n * @param years - Number of years\n * @returns CAGR as a percentage\n */\nexport function calculateCAGR(\n  initialValue: number,\n  finalValue: number,\n  years: number\n): number {\n  if (initialValue <= 0 || finalValue <= 0 || years <= 0) {\n    return 0;\n  }\n  \n  const cagr = (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100;\n  return Number(cagr.toFixed(CALCULATION_CONFIG.precision.cagr));\n}\n\n/**\n * Calculate absolute return\n * @param initialValue - Initial investment value\n * @param finalValue - Final investment value\n * @returns Absolute return as a percentage\n */\nexport function calculateAbsoluteReturn(\n  initialValue: number,\n  finalValue: number\n): number {\n  if (initialValue <= 0) return 0;\n  \n  const absoluteReturn = ((finalValue - initialValue) / initialValue) * 100;\n  return Number(absoluteReturn.toFixed(CALCULATION_CONFIG.precision.percentage));\n}\n\n/**\n * Calculate the number of years between two dates\n * @param startDate - Start date\n * @param endDate - End date\n * @returns Number of years (with decimals)\n */\nexport function calculateYearsBetweenDates(\n  startDate: Date,\n  endDate: Date\n): number {\n  const timeDiff = endDate.getTime() - startDate.getTime();\n  const daysDiff = timeDiff / (1000 * 3600 * 24);\n  return daysDiff / 365.25; // Account for leap years\n}\n\n/**\n * Format currency value\n * @param value - Numeric value\n * @param currency - Currency code (default: INR)\n * @returns Formatted currency string\n */\nexport function formatCurrency(value: number, currency: string = 'INR'): string {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: CALCULATION_CONFIG.precision.currency,\n    maximumFractionDigits: CALCULATION_CONFIG.precision.currency,\n  }).format(value);\n}\n\n/**\n * Format percentage value\n * @param value - Numeric value\n * @returns Formatted percentage string\n */\nexport function formatPercentage(value: number): string {\n  return `${value.toFixed(CALCULATION_CONFIG.precision.percentage)}%`;\n}\n\n/**\n * Format large numbers with Indian numbering system\n * @param value - Numeric value\n * @returns Formatted number string (e.g., 1,00,000)\n */\nexport function formatIndianNumber(value: number): string {\n  return new Intl.NumberFormat('en-IN').format(value);\n}\n\n/**\n * Validate date range\n * @param startDate - Start date\n * @param endDate - End date\n * @returns Validation result\n */\nexport function validateDateRange(startDate: Date, endDate: Date): {\n  isValid: boolean;\n  error?: string;\n} {\n  const now = new Date();\n\n  if (startDate >= endDate) {\n    return { isValid: false, error: 'Start date must be before end date' };\n  }\n\n  if (startDate > now) {\n    return { isValid: false, error: 'Start date cannot be in the future' };\n  }\n\n  if (endDate > now) {\n    return { isValid: false, error: 'End date cannot be in the future' };\n  }\n\n  // Angel One API historical data limits (based on testing)\n  const angelOneMinDate = new Date('2018-01-01'); // Conservative estimate - Angel One reliable data from 2018\n  if (startDate < angelOneMinDate) {\n    return {\n      isValid: false,\n      error: `Start date cannot be earlier than ${angelOneMinDate.toLocaleDateString()}. Angel One API historical data is limited to recent years.`\n    };\n  }\n\n  // Maximum date range validation (Angel One may have limits on large ranges)\n  const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));\n  const maxDays = 3650; // ~10 years maximum\n\n  if (daysDifference > maxDays) {\n    return {\n      isValid: false,\n      error: `Date range too large (${daysDifference} days). Maximum allowed is ${maxDays} days (~10 years).`\n    };\n  }\n\n  // Minimum date range validation\n  const minDays = 30; // At least 1 month for meaningful analysis\n  if (daysDifference < minDays) {\n    return {\n      isValid: false,\n      error: `Date range too small (${daysDifference} days). Minimum required is ${minDays} days for meaningful analysis.`\n    };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Generate unique ID\n * @returns Unique string ID\n */\nexport function generateId(): string {\n  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * Debounce function\n * @param func - Function to debounce\n * @param wait - Wait time in milliseconds\n * @returns Debounced function\n */\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Check if market is open\n * @returns Boolean indicating if market is currently open\n */\nexport function isMarketOpen(): boolean {\n  const now = new Date();\n  const currentTime = now.toLocaleTimeString('en-IN', {\n    timeZone: 'Asia/Kolkata',\n    hour12: false,\n  });\n  \n  const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;\n  const isWithinTradingHours = currentTime >= '09:15:00' && currentTime <= '15:30:00';\n  \n  return isWeekday && isWithinTradingHours;\n}\n\n/**\n * Safe JSON parse\n * @param jsonString - JSON string to parse\n * @param defaultValue - Default value if parsing fails\n * @returns Parsed object or default value\n */\nexport function safeJsonParse<T>(jsonString: string, defaultValue: T): T {\n  try {\n    return JSON.parse(jsonString);\n  } catch {\n    return defaultValue;\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;AAE7D;;AASO,SAAS,cACd,YAAoB,EACpB,UAAkB,EAClB,KAAa;IAEb,IAAI,gBAAgB,KAAK,cAAc,KAAK,SAAS,GAAG;QACtD,OAAO;IACT;IAEA,MAAM,OAAO,CAAC,KAAK,GAAG,CAAC,aAAa,cAAc,IAAI,SAAS,CAAC,IAAI;IACpE,OAAO,OAAO,KAAK,OAAO,CAAC,gIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,IAAI;AAC9D;AAQO,SAAS,wBACd,YAAoB,EACpB,UAAkB;IAElB,IAAI,gBAAgB,GAAG,OAAO;IAE9B,MAAM,iBAAiB,AAAC,CAAC,aAAa,YAAY,IAAI,eAAgB;IACtE,OAAO,OAAO,eAAe,OAAO,CAAC,gIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,UAAU;AAC9E;AAQO,SAAS,2BACd,SAAe,EACf,OAAa;IAEb,MAAM,WAAW,QAAQ,OAAO,KAAK,UAAU,OAAO;IACtD,MAAM,WAAW,WAAW,CAAC,OAAO,OAAO,EAAE;IAC7C,OAAO,WAAW,QAAQ,yBAAyB;AACrD;AAQO,SAAS,eAAe,KAAa,EAAE,WAAmB,KAAK;IACpE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB,gIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,QAAQ;QAC5D,uBAAuB,gIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,QAAQ;IAC9D,GAAG,MAAM,CAAC;AACZ;AAOO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,MAAM,OAAO,CAAC,gIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;AACrE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAQO,SAAS,kBAAkB,SAAe,EAAE,OAAa;IAI9D,MAAM,MAAM,IAAI;IAEhB,IAAI,aAAa,SAAS;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;IAEA,IAAI,YAAY,KAAK;QACnB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;IAEA,IAAI,UAAU,KAAK;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmC;IACrE;IAEA,0DAA0D;IAC1D,MAAM,kBAAkB,IAAI,KAAK,eAAe,4DAA4D;IAC5G,IAAI,YAAY,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,kCAAkC,EAAE,gBAAgB,kBAAkB,GAAG,2DAA2D,CAAC;QAC/I;IACF;IAEA,4EAA4E;IAC5E,MAAM,iBAAiB,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IACjG,MAAM,UAAU,MAAM,oBAAoB;IAE1C,IAAI,iBAAiB,SAAS;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,eAAe,2BAA2B,EAAE,QAAQ,kBAAkB,CAAC;QACzG;IACF;IAEA,gCAAgC;IAChC,MAAM,UAAU,IAAI,2CAA2C;IAC/D,IAAI,iBAAiB,SAAS;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,eAAe,4BAA4B,EAAE,QAAQ,8BAA8B,CAAC;QACtH;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAMO,SAAS;IACd,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AACnE;AAQO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAMO,SAAS;IACd,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,IAAI,kBAAkB,CAAC,SAAS;QAClD,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,YAAY,IAAI,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM;IACvD,MAAM,uBAAuB,eAAe,cAAc,eAAe;IAEzE,OAAO,aAAa;AACtB;AAQO,SAAS,cAAiB,UAAkB,EAAE,YAAe;IAClE,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/components/tables/ComparisonTable.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\n\nexport interface ComparisonTableData {\n  name: string;\n  type: string;\n  initialValue: number;\n  currentValue: number;\n  totalReturn: number;\n  cagr: number;\n  absoluteReturn: number;\n  rank: number;\n  outperformance?: number;\n}\n\ninterface ComparisonTableProps {\n  data: ComparisonTableData[];\n  investmentName: string;\n  title?: string;\n  showOutperformance?: boolean;\n}\n\ntype SortField = 'name' | 'cagr' | 'absoluteReturn' | 'currentValue' | 'totalReturn' | 'rank';\ntype SortDirection = 'asc' | 'desc';\n\nconst ComparisonTable: React.FC<ComparisonTableProps> = ({\n  data,\n  investmentName,\n  title = 'Investment Performance Comparison',\n  showOutperformance = true,\n}) => {\n  const [sortField, setSortField] = useState<SortField>('rank');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');\n\n  // Sort data based on current sort field and direction\n  const sortedData = [...data].sort((a, b) => {\n    let aValue = a[sortField];\n    let bValue = b[sortField];\n\n    if (typeof aValue === 'string') {\n      aValue = aValue.toLowerCase();\n      bValue = (bValue as string).toLowerCase();\n    }\n\n    if (sortDirection === 'asc') {\n      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n    } else {\n      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n    }\n  });\n\n  // Handle column header click for sorting\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('desc'); // Default to descending for numeric fields\n    }\n  };\n\n  // Get sort icon\n  const getSortIcon = (field: SortField) => {\n    if (sortField !== field) return '↕️';\n    return sortDirection === 'asc' ? '↑' : '↓';\n  };\n\n  // Get row styling based on performance\n  const getRowStyling = (item: ComparisonTableData) => {\n    if (item.name.toLowerCase().includes(investmentName.toLowerCase()) || \n        item.name.toLowerCase().includes('investment')) {\n      return 'bg-blue-50 border-l-4 border-blue-500';\n    }\n    \n    if (item.rank === 1) {\n      return 'bg-green-50 border-l-4 border-green-500';\n    }\n    \n    if (item.rank === data.length) {\n      return 'bg-red-50 border-l-4 border-red-300';\n    }\n    \n    return 'bg-white hover:bg-gray-50';\n  };\n\n  // Get performance indicator\n  const getPerformanceIndicator = (cagr: number) => {\n    if (cagr > 15) return { icon: '🚀', color: 'text-green-600', label: 'Excellent' };\n    if (cagr > 10) return { icon: '📈', color: 'text-green-500', label: 'Good' };\n    if (cagr > 5) return { icon: '📊', color: 'text-yellow-500', label: 'Average' };\n    if (cagr > 0) return { icon: '📉', color: 'text-orange-500', label: 'Below Average' };\n    return { icon: '❌', color: 'text-red-500', label: 'Loss' };\n  };\n\n  const TableHeader: React.FC<{ field: SortField; children: React.ReactNode; className?: string }> = ({\n    field,\n    children,\n    className = '',\n  }) => (\n    <th\n      className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 ${className}`}\n      onClick={() => handleSort(field)}\n    >\n      <div className=\"flex items-center space-x-1\">\n        <span>{children}</span>\n        <span className=\"text-gray-400\">{getSortIcon(field)}</span>\n      </div>\n    </th>\n  );\n\n  return (\n    <div className=\"w-full\">\n      {title && (\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4 text-center\">\n          {title}\n        </h3>\n      )}\n\n      <div className=\"overflow-x-auto shadow-lg rounded-lg\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <TableHeader field=\"rank\" className=\"w-16\">\n                Rank\n              </TableHeader>\n              <TableHeader field=\"name\" className=\"min-w-48\">\n                Investment Option\n              </TableHeader>\n              <TableHeader field=\"cagr\">\n                CAGR\n              </TableHeader>\n              <TableHeader field=\"absoluteReturn\">\n                Total Return\n              </TableHeader>\n              <TableHeader field=\"currentValue\">\n                Current Value\n              </TableHeader>\n              <TableHeader field=\"totalReturn\">\n                Profit/Loss\n              </TableHeader>\n              {showOutperformance && (\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  vs {investmentName}\n                </th>\n              )}\n              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Performance\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {sortedData.map((item, index) => {\n              const performance = getPerformanceIndicator(item.cagr);\n              \n              return (\n                <tr key={item.name} className={getRowStyling(item)}>\n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${\n                        item.rank === 1 ? 'bg-green-500 text-white' : \n                        item.rank === data.length ? 'bg-red-400 text-white' : \n                        'bg-gray-400 text-white'\n                      }`}>\n                        {item.rank}\n                      </span>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {item.name}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {item.type}\n                      </div>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <div className={`text-sm font-semibold ${\n                      item.cagr > 0 ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {formatPercentage(item.cagr)}\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <div className={`text-sm font-semibold ${\n                      item.absoluteReturn > 0 ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {formatPercentage(item.absoluteReturn)}\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {formatCurrency(item.currentValue)}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      from {formatCurrency(item.initialValue)}\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <div className={`text-sm font-semibold ${\n                      item.totalReturn > 0 ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {item.totalReturn > 0 ? '+' : ''}{formatCurrency(item.totalReturn)}\n                    </div>\n                  </td>\n                  \n                  {showOutperformance && (\n                    <td className=\"px-4 py-4 whitespace-nowrap\">\n                      {item.outperformance !== undefined && (\n                        <div className={`text-sm font-semibold ${\n                          item.outperformance > 0 ? 'text-green-600' : \n                          item.outperformance < 0 ? 'text-red-600' : 'text-gray-600'\n                        }`}>\n                          {item.outperformance > 0 ? '+' : ''}{formatPercentage(item.outperformance)}\n                        </div>\n                      )}\n                    </td>\n                  )}\n                  \n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <span className=\"mr-2\">{performance.icon}</span>\n                      <span className={`text-sm font-medium ${performance.color}`}>\n                        {performance.label}\n                      </span>\n                    </div>\n                  </td>\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Summary Statistics */}\n      <div className=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div className=\"bg-blue-50 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-blue-800 mb-2\">Best Performer</h4>\n          <div className=\"text-lg font-bold text-blue-600\">\n            {sortedData.find(item => item.rank === 1)?.name}\n          </div>\n          <div className=\"text-sm text-blue-500\">\n            {formatPercentage(sortedData.find(item => item.rank === 1)?.cagr || 0)} CAGR\n          </div>\n        </div>\n        \n        <div className=\"bg-green-50 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-green-800 mb-2\">Average CAGR</h4>\n          <div className=\"text-lg font-bold text-green-600\">\n            {formatPercentage(data.reduce((sum, item) => sum + item.cagr, 0) / data.length)}\n          </div>\n          <div className=\"text-sm text-green-500\">\n            Across all options\n          </div>\n        </div>\n        \n        <div className=\"bg-purple-50 p-4 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-purple-800 mb-2\">Your Investment</h4>\n          <div className=\"text-lg font-bold text-purple-600\">\n            Rank #{data.find(item => \n              item.name.toLowerCase().includes(investmentName.toLowerCase()) ||\n              item.name.toLowerCase().includes('investment')\n            )?.rank || 'N/A'}\n          </div>\n          <div className=\"text-sm text-purple-500\">\n            Out of {data.length} options\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ComparisonTable;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA2BA,MAAM,kBAAkD,CAAC,EACvD,IAAI,EACJ,cAAc,EACd,QAAQ,mCAAmC,EAC3C,qBAAqB,IAAI,EAC1B;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,sDAAsD;IACtD,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAAC,CAAC,GAAG;QACpC,IAAI,SAAS,CAAC,CAAC,UAAU;QACzB,IAAI,SAAS,CAAC,CAAC,UAAU;QAEzB,IAAI,OAAO,WAAW,UAAU;YAC9B,SAAS,OAAO,WAAW;YAC3B,SAAS,AAAC,OAAkB,WAAW;QACzC;QAEA,IAAI,kBAAkB,OAAO;YAC3B,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;QACtD,OAAO;YACL,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;QACtD;IACF;IAEA,yCAAyC;IACzC,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,aAAa;YACb,iBAAiB,SAAS,2CAA2C;QACvE;IACF;IAEA,gBAAgB;IAChB,MAAM,cAAc,CAAC;QACnB,IAAI,cAAc,OAAO,OAAO;QAChC,OAAO,kBAAkB,QAAQ,MAAM;IACzC;IAEA,uCAAuC;IACvC,MAAM,gBAAgB,CAAC;QACrB,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,OAC3D,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe;YAClD,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,KAAK,GAAG;YACnB,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM,EAAE;YAC7B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,0BAA0B,CAAC;QAC/B,IAAI,OAAO,IAAI,OAAO;YAAE,MAAM;YAAM,OAAO;YAAkB,OAAO;QAAY;QAChF,IAAI,OAAO,IAAI,OAAO;YAAE,MAAM;YAAM,OAAO;YAAkB,OAAO;QAAO;QAC3E,IAAI,OAAO,GAAG,OAAO;YAAE,MAAM;YAAM,OAAO;YAAmB,OAAO;QAAU;QAC9E,IAAI,OAAO,GAAG,OAAO;YAAE,MAAM;YAAM,OAAO;YAAmB,OAAO;QAAgB;QACpF,OAAO;YAAE,MAAM;YAAK,OAAO;YAAgB,OAAO;QAAO;IAC3D;IAEA,MAAM,cAA6F,CAAC,EAClG,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACf,iBACC,6LAAC;YACC,WAAW,CAAC,gHAAgH,EAAE,WAAW;YACzI,SAAS,IAAM,WAAW;sBAE1B,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAM;;;;;;kCACP,6LAAC;wBAAK,WAAU;kCAAiB,YAAY;;;;;;;;;;;;;;;;;IAKnD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAIL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCAAY,OAAM;wCAAO,WAAU;kDAAO;;;;;;kDAG3C,6LAAC;wCAAY,OAAM;wCAAO,WAAU;kDAAW;;;;;;kDAG/C,6LAAC;wCAAY,OAAM;kDAAO;;;;;;kDAG1B,6LAAC;wCAAY,OAAM;kDAAiB;;;;;;kDAGpC,6LAAC;wCAAY,OAAM;kDAAe;;;;;;kDAGlC,6LAAC;wCAAY,OAAM;kDAAc;;;;;;oCAGhC,oCACC,6LAAC;wCAAG,WAAU;;4CAAiF;4CACzF;;;;;;;kDAGR,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,6LAAC;4BAAM,WAAU;sCACd,WAAW,GAAG,CAAC,CAAC,MAAM;gCACrB,MAAM,cAAc,wBAAwB,KAAK,IAAI;gCAErD,qBACE,6LAAC;oCAAmB,WAAW,cAAc;;sDAC3C,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAW,CAAC,+EAA+E,EAC/F,KAAK,IAAI,KAAK,IAAI,4BAClB,KAAK,IAAI,KAAK,KAAK,MAAM,GAAG,0BAC5B,0BACA;8DACC,KAAK,IAAI;;;;;;;;;;;;;;;;sDAKhB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAW,CAAC,sBAAsB,EACrC,KAAK,IAAI,GAAG,IAAI,mBAAmB,gBACnC;0DACC,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;sDAI/B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAW,CAAC,sBAAsB,EACrC,KAAK,cAAc,GAAG,IAAI,mBAAmB,gBAC7C;0DACC,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,cAAc;;;;;;;;;;;sDAIzC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;wDAAwB;wDAC/B,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY;;;;;;;;;;;;;sDAI1C,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAW,CAAC,sBAAsB,EACrC,KAAK,WAAW,GAAG,IAAI,mBAAmB,gBAC1C;;oDACC,KAAK,WAAW,GAAG,IAAI,MAAM;oDAAI,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW;;;;;;;;;;;;wCAIpE,oCACC,6LAAC;4CAAG,WAAU;sDACX,KAAK,cAAc,KAAK,2BACvB,6LAAC;gDAAI,WAAW,CAAC,sBAAsB,EACrC,KAAK,cAAc,GAAG,IAAI,mBAC1B,KAAK,cAAc,GAAG,IAAI,iBAAiB,iBAC3C;;oDACC,KAAK,cAAc,GAAG,IAAI,MAAM;oDAAI,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,cAAc;;;;;;;;;;;;sDAMjF,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAQ,YAAY,IAAI;;;;;;kEACxC,6LAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,YAAY,KAAK,EAAE;kEACxD,YAAY,KAAK;;;;;;;;;;;;;;;;;;mCA1EjB,KAAK,IAAI;;;;;4BAgFtB;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACZ,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,IAAI;;;;;;0CAE7C,6LAAC;gCAAI,WAAU;;oCACZ,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,IAAI,QAAQ;oCAAG;;;;;;;;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAC1D,6LAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE,KAAK,KAAK,MAAM;;;;;;0CAEhF,6LAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;kCAK1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,6LAAC;gCAAI,WAAU;;oCAAoC;oCAC1C,KAAK,IAAI,CAAC,CAAA,OACf,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,OAC3D,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAChC,QAAQ;;;;;;;0CAEb,6LAAC;gCAAI,WAAU;;oCAA0B;oCAC/B,KAAK,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAMhC;GA5PM;KAAA;uCA8PS", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/components/charts/TimeSeriesChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n} from 'recharts';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\nimport { UI_CONFIG } from '@/lib/config';\n\nexport interface TimeSeriesDataPoint {\n  date: string;\n  investment: number;\n  gold: number;\n  fd: number;\n  nifty: number;\n}\n\ninterface TimeSeriesChartProps {\n  data: TimeSeriesDataPoint[];\n  title?: string;\n  height?: number;\n  showLegend?: boolean;\n  investmentLabel?: string;\n}\n\nconst TimeSeriesChart: React.FC<TimeSeriesChartProps> = ({\n  data,\n  title = 'Investment Performance Comparison',\n  height = UI_CONFIG.charts.defaultHeight,\n  showLegend = true,\n  investmentLabel = 'Your Investment',\n}) => {\n  // Custom tooltip formatter\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-white p-4 border border-gray-300 rounded-lg shadow-lg\">\n          <p className=\"font-semibold text-gray-800 mb-2\">{`Date: ${label}`}</p>\n          {payload.map((entry: any, index: number) => (\n            <p key={index} style={{ color: entry.color }} className=\"text-sm\">\n              {`${entry.name}: ${formatCurrency(entry.value)}`}\n            </p>\n          ))}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  // Format Y-axis values\n  const formatYAxis = (value: number) => {\n    if (value >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else if (value >= 1000) {\n      return `₹${(value / 1000).toFixed(1)}K`;\n    }\n    return `₹${value}`;\n  };\n\n  // Format X-axis dates\n  const formatXAxis = (tickItem: string) => {\n    const date = new Date(tickItem);\n    return date.toLocaleDateString('en-IN', { \n      month: 'short', \n      day: 'numeric' \n    });\n  };\n\n  return (\n    <div className=\"w-full\">\n      {title && (\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4 text-center\">\n          {title}\n        </h3>\n      )}\n      \n      <ResponsiveContainer width=\"100%\" height={height}>\n        <LineChart\n          data={data}\n          margin={{\n            top: 20,\n            right: 30,\n            left: 20,\n            bottom: 20,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n          \n          <XAxis\n            dataKey=\"date\"\n            tickFormatter={formatXAxis}\n            stroke=\"#666\"\n            fontSize={12}\n            tick={{ fill: '#666' }}\n          />\n          \n          <YAxis\n            tickFormatter={formatYAxis}\n            stroke=\"#666\"\n            fontSize={12}\n            tick={{ fill: '#666' }}\n          />\n          \n          <Tooltip content={<CustomTooltip />} />\n          \n          {showLegend && (\n            <Legend\n              wrapperStyle={{\n                paddingTop: '20px',\n                fontSize: '14px',\n              }}\n            />\n          )}\n          \n          <Line\n            type=\"monotone\"\n            dataKey=\"investment\"\n            stroke={UI_CONFIG.charts.colors.investment}\n            strokeWidth={3}\n            dot={{ fill: UI_CONFIG.charts.colors.investment, strokeWidth: 2, r: 4 }}\n            activeDot={{ r: 6, stroke: UI_CONFIG.charts.colors.investment, strokeWidth: 2 }}\n            name={investmentLabel}\n          />\n          \n          <Line\n            type=\"monotone\"\n            dataKey=\"nifty\"\n            stroke={UI_CONFIG.charts.colors.nifty}\n            strokeWidth={2}\n            dot={{ fill: UI_CONFIG.charts.colors.nifty, strokeWidth: 1, r: 3 }}\n            name=\"Nifty 50\"\n          />\n          \n          <Line\n            type=\"monotone\"\n            dataKey=\"gold\"\n            stroke={UI_CONFIG.charts.colors.gold}\n            strokeWidth={2}\n            dot={{ fill: UI_CONFIG.charts.colors.gold, strokeWidth: 1, r: 3 }}\n            name=\"Gold\"\n          />\n          \n          <Line\n            type=\"monotone\"\n            dataKey=\"fd\"\n            stroke={UI_CONFIG.charts.colors.fd}\n            strokeWidth={2}\n            dot={{ fill: UI_CONFIG.charts.colors.fd, strokeWidth: 1, r: 3 }}\n            name=\"Fixed Deposit\"\n          />\n        </LineChart>\n      </ResponsiveContainer>\n      \n      {/* Performance Summary */}\n      <div className=\"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n        {data.length > 0 && (\n          <>\n            <div className=\"text-center p-2 bg-blue-50 rounded\">\n              <div className=\"font-semibold text-blue-800\">Your Investment</div>\n              <div className=\"text-blue-600\">\n                {formatCurrency(data[data.length - 1].investment)}\n              </div>\n            </div>\n            \n            <div className=\"text-center p-2 bg-purple-50 rounded\">\n              <div className=\"font-semibold text-purple-800\">Nifty 50</div>\n              <div className=\"text-purple-600\">\n                {formatCurrency(data[data.length - 1].nifty)}\n              </div>\n            </div>\n            \n            <div className=\"text-center p-2 bg-yellow-50 rounded\">\n              <div className=\"font-semibold text-yellow-800\">Gold</div>\n              <div className=\"text-yellow-600\">\n                {formatCurrency(data[data.length - 1].gold)}\n              </div>\n            </div>\n            \n            <div className=\"text-center p-2 bg-green-50 rounded\">\n              <div className=\"font-semibold text-green-800\">Fixed Deposit</div>\n              <div className=\"text-green-600\">\n                {formatCurrency(data[data.length - 1].fd)}\n              </div>\n            </div>\n          </>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TimeSeriesChart;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAdA;;;;;AAgCA,MAAM,kBAAkD,CAAC,EACvD,IAAI,EACJ,QAAQ,mCAAmC,EAC3C,SAAS,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,aAAa,EACvC,aAAa,IAAI,EACjB,kBAAkB,iBAAiB,EACpC;IACC,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAoC,CAAC,MAAM,EAAE,OAAO;;;;;;oBAChE,QAAQ,GAAG,CAAC,CAAC,OAAY,sBACxB,6LAAC;4BAAc,OAAO;gCAAE,OAAO,MAAM,KAAK;4BAAC;4BAAG,WAAU;sCACrD,GAAG,MAAM,IAAI,CAAC,EAAE,EAAE,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK,GAAG;2BAD1C;;;;;;;;;;;QAMhB;QACA,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,QAAQ;YACnB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,IAAI,SAAS,MAAM;YACxB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC;QACA,OAAO,CAAC,CAAC,EAAE,OAAO;IACpB;IAEA,sBAAsB;IACtB,MAAM,cAAc,CAAC;QACnB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAIL,6LAAC,sKAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAO,QAAQ;0BACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;oBACR,MAAM;oBACN,QAAQ;wBACN,KAAK;wBACL,OAAO;wBACP,MAAM;wBACN,QAAQ;oBACV;;sCAEA,6LAAC,gKAAA,CAAA,gBAAa;4BAAC,iBAAgB;4BAAM,QAAO;;;;;;sCAE5C,6LAAC,wJAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,eAAe;4BACf,QAAO;4BACP,UAAU;4BACV,MAAM;gCAAE,MAAM;4BAAO;;;;;;sCAGvB,6LAAC,wJAAA,CAAA,QAAK;4BACJ,eAAe;4BACf,QAAO;4BACP,UAAU;4BACV,MAAM;gCAAE,MAAM;4BAAO;;;;;;sCAGvB,6LAAC,0JAAA,CAAA,UAAO;4BAAC,uBAAS,6LAAC;;;;;;;;;;wBAElB,4BACC,6LAAC,yJAAA,CAAA,SAAM;4BACL,cAAc;gCACZ,YAAY;gCACZ,UAAU;4BACZ;;;;;;sCAIJ,6LAAC,uJAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,QAAQ,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU;4BAC1C,aAAa;4BACb,KAAK;gCAAE,MAAM,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU;gCAAE,aAAa;gCAAG,GAAG;4BAAE;4BACtE,WAAW;gCAAE,GAAG;gCAAG,QAAQ,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU;gCAAE,aAAa;4BAAE;4BAC9E,MAAM;;;;;;sCAGR,6LAAC,uJAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,QAAQ,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;4BACrC,aAAa;4BACb,KAAK;gCAAE,MAAM,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;gCAAE,aAAa;gCAAG,GAAG;4BAAE;4BACjE,MAAK;;;;;;sCAGP,6LAAC,uJAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,QAAQ,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI;4BACpC,aAAa;4BACb,KAAK;gCAAE,MAAM,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI;gCAAE,aAAa;gCAAG,GAAG;4BAAE;4BAChE,MAAK;;;;;;sCAGP,6LAAC,uJAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,QAAQ,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;4BAClC,aAAa;4BACb,KAAK;gCAAE,MAAM,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gCAAE,aAAa;gCAAG,GAAG;4BAAE;4BAC9D,MAAK;;;;;;;;;;;;;;;;;0BAMX,6LAAC;gBAAI,WAAU;0BACZ,KAAK,MAAM,GAAG,mBACb;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,UAAU;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;;;;;;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI;;;;;;;;;;;;sCAI9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA+B;;;;;;8CAC9C,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD;KApKM;uCAsKS", "debugId": null}}, {"offset": {"line": 1205, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/components/charts/ComparisonBarChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  Cell,\n} from 'recharts';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\nimport { UI_CONFIG } from '@/lib/config';\n\nexport interface ComparisonBarData {\n  name: string;\n  cagr: number;\n  absoluteReturn: number;\n  currentValue: number;\n  initialValue?: number;\n}\n\ninterface ComparisonBarChartProps {\n  data: ComparisonBarData[];\n  title?: string;\n  height?: number;\n  showLegend?: boolean;\n  metric?: 'cagr' | 'absoluteReturn' | 'currentValue';\n  highlightBest?: boolean;\n}\n\nconst ComparisonBarChart: React.FC<ComparisonBarChartProps> = ({\n  data,\n  title = 'Investment Performance Comparison',\n  height = UI_CONFIG.charts.defaultHeight,\n  showLegend = true,\n  metric = 'cagr',\n  highlightBest = true,\n}) => {\n  // Sort data by the selected metric\n  const sortedData = [...data].sort((a, b) => b[metric] - a[metric]);\n  \n  // Get colors for bars\n  const getBarColor = (name: string, index: number) => {\n    if (name.toLowerCase().includes('investment') || name.toLowerCase().includes('stock')) {\n      return UI_CONFIG.charts.colors.investment;\n    } else if (name.toLowerCase().includes('nifty')) {\n      return UI_CONFIG.charts.colors.nifty;\n    } else if (name.toLowerCase().includes('gold')) {\n      return UI_CONFIG.charts.colors.gold;\n    } else if (name.toLowerCase().includes('fd') || name.toLowerCase().includes('deposit')) {\n      return UI_CONFIG.charts.colors.fd;\n    }\n    \n    // Default colors for other items\n    const defaultColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];\n    return defaultColors[index % defaultColors.length];\n  };\n\n  // Custom tooltip formatter\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <div className=\"bg-white p-4 border border-gray-300 rounded-lg shadow-lg\">\n          <p className=\"font-semibold text-gray-800 mb-2\">{label}</p>\n          <p className=\"text-sm text-blue-600\">\n            CAGR: {formatPercentage(data.cagr)}\n          </p>\n          <p className=\"text-sm text-green-600\">\n            Absolute Return: {formatPercentage(data.absoluteReturn)}\n          </p>\n          <p className=\"text-sm text-purple-600\">\n            Current Value: {formatCurrency(data.currentValue)}\n          </p>\n          {data.initialValue && (\n            <p className=\"text-sm text-gray-600\">\n              Initial Value: {formatCurrency(data.initialValue)}\n            </p>\n          )}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  // Format Y-axis based on metric\n  const formatYAxis = (value: number) => {\n    if (metric === 'currentValue') {\n      if (value >= 100000) {\n        return `₹${(value / 100000).toFixed(1)}L`;\n      } else if (value >= 1000) {\n        return `₹${(value / 1000).toFixed(1)}K`;\n      }\n      return `₹${value}`;\n    } else {\n      return `${value}%`;\n    }\n  };\n\n  // Get metric label\n  const getMetricLabel = () => {\n    switch (metric) {\n      case 'cagr':\n        return 'CAGR (%)';\n      case 'absoluteReturn':\n        return 'Absolute Return (%)';\n      case 'currentValue':\n        return 'Current Value (₹)';\n      default:\n        return 'Value';\n    }\n  };\n\n  return (\n    <div className=\"w-full\">\n      {title && (\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4 text-center\">\n          {title}\n        </h3>\n      )}\n      \n      <ResponsiveContainer width=\"100%\" height={height}>\n        <BarChart\n          data={sortedData}\n          margin={{\n            top: 20,\n            right: 30,\n            left: 20,\n            bottom: 60,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n          \n          <XAxis\n            dataKey=\"name\"\n            stroke=\"#666\"\n            fontSize={12}\n            tick={{ fill: '#666' }}\n            angle={-45}\n            textAnchor=\"end\"\n            height={80}\n          />\n          \n          <YAxis\n            tickFormatter={formatYAxis}\n            stroke=\"#666\"\n            fontSize={12}\n            tick={{ fill: '#666' }}\n            label={{ \n              value: getMetricLabel(), \n              angle: -90, \n              position: 'insideLeft',\n              style: { textAnchor: 'middle' }\n            }}\n          />\n          \n          <Tooltip content={<CustomTooltip />} />\n          \n          {showLegend && (\n            <Legend\n              wrapperStyle={{\n                paddingTop: '20px',\n                fontSize: '14px',\n              }}\n            />\n          )}\n          \n          <Bar\n            dataKey={metric}\n            name={getMetricLabel()}\n            radius={[4, 4, 0, 0]}\n          >\n            {sortedData.map((entry, index) => (\n              <Cell \n                key={`cell-${index}`} \n                fill={getBarColor(entry.name, index)}\n                stroke={highlightBest && index === 0 ? '#000' : 'none'}\n                strokeWidth={highlightBest && index === 0 ? 2 : 0}\n              />\n            ))}\n          </Bar>\n        </BarChart>\n      </ResponsiveContainer>\n      \n      {/* Performance Rankings */}\n      <div className=\"mt-4\">\n        <h4 className=\"text-md font-semibold text-gray-700 mb-2\">\n          Performance Ranking ({getMetricLabel()})\n        </h4>\n        <div className=\"space-y-2\">\n          {sortedData.map((item, index) => (\n            <div \n              key={item.name}\n              className={`flex justify-between items-center p-2 rounded ${\n                index === 0 ? 'bg-green-50 border border-green-200' : 'bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-center\">\n                <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold mr-3 ${\n                  index === 0 ? 'bg-green-500 text-white' : 'bg-gray-400 text-white'\n                }`}>\n                  {index + 1}\n                </span>\n                <span className=\"font-medium\">{item.name}</span>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"font-semibold\">\n                  {metric === 'currentValue' \n                    ? formatCurrency(item[metric])\n                    : formatPercentage(item[metric])\n                  }\n                </div>\n                {metric !== 'currentValue' && (\n                  <div className=\"text-sm text-gray-600\">\n                    {formatCurrency(item.currentValue)}\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ComparisonBarChart;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAfA;;;;;AAkCA,MAAM,qBAAwD,CAAC,EAC7D,IAAI,EACJ,QAAQ,mCAAmC,EAC3C,SAAS,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,aAAa,EACvC,aAAa,IAAI,EACjB,SAAS,MAAM,EACf,gBAAgB,IAAI,EACrB;IACC,mCAAmC;IACnC,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO;IAEjE,sBAAsB;IACtB,MAAM,cAAc,CAAC,MAAc;QACjC,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,iBAAiB,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;YACrF,OAAO,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU;QAC3C,OAAO,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;YAC/C,OAAO,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;QACtC,OAAO,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,SAAS;YAC9C,OAAO,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI;QACrC,OAAO,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,SAAS,KAAK,WAAW,GAAG,QAAQ,CAAC,YAAY;YACtF,OAAO,gIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QACnC;QAEA,iCAAiC;QACjC,MAAM,gBAAgB;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;QAC7E,OAAO,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;IACpD;IAEA,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAoC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;;4BAAwB;4BAC5B,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;;;;;;;kCAEnC,6LAAC;wBAAE,WAAU;;4BAAyB;4BAClB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,cAAc;;;;;;;kCAExD,6LAAC;wBAAE,WAAU;;4BAA0B;4BACrB,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY;;;;;;;oBAEjD,KAAK,YAAY,kBAChB,6LAAC;wBAAE,WAAU;;4BAAwB;4BACnB,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY;;;;;;;;;;;;;QAK1D;QACA,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,gBAAgB;YAC7B,IAAI,SAAS,QAAQ;gBACnB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3C,OAAO,IAAI,SAAS,MAAM;gBACxB,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YACzC;YACA,OAAO,CAAC,CAAC,EAAE,OAAO;QACpB,OAAO;YACL,OAAO,GAAG,MAAM,CAAC,CAAC;QACpB;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAIL,6LAAC,sKAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAO,QAAQ;0BACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;oBACP,MAAM;oBACN,QAAQ;wBACN,KAAK;wBACL,OAAO;wBACP,MAAM;wBACN,QAAQ;oBACV;;sCAEA,6LAAC,gKAAA,CAAA,gBAAa;4BAAC,iBAAgB;4BAAM,QAAO;;;;;;sCAE5C,6LAAC,wJAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,QAAO;4BACP,UAAU;4BACV,MAAM;gCAAE,MAAM;4BAAO;4BACrB,OAAO,CAAC;4BACR,YAAW;4BACX,QAAQ;;;;;;sCAGV,6LAAC,wJAAA,CAAA,QAAK;4BACJ,eAAe;4BACf,QAAO;4BACP,UAAU;4BACV,MAAM;gCAAE,MAAM;4BAAO;4BACrB,OAAO;gCACL,OAAO;gCACP,OAAO,CAAC;gCACR,UAAU;gCACV,OAAO;oCAAE,YAAY;gCAAS;4BAChC;;;;;;sCAGF,6LAAC,0JAAA,CAAA,UAAO;4BAAC,uBAAS,6LAAC;;;;;;;;;;wBAElB,4BACC,6LAAC,yJAAA,CAAA,SAAM;4BACL,cAAc;gCACZ,YAAY;gCACZ,UAAU;4BACZ;;;;;;sCAIJ,6LAAC,sJAAA,CAAA,MAAG;4BACF,SAAS;4BACT,MAAM;4BACN,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE;sCAEnB,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC,uJAAA,CAAA,OAAI;oCAEH,MAAM,YAAY,MAAM,IAAI,EAAE;oCAC9B,QAAQ,iBAAiB,UAAU,IAAI,SAAS;oCAChD,aAAa,iBAAiB,UAAU,IAAI,IAAI;mCAH3C,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;0BAW9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA2C;4BACjC;4BAAiB;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;gCAEC,WAAW,CAAC,8CAA8C,EACxD,UAAU,IAAI,wCAAwC,cACtD;;kDAEF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,CAAC,oFAAoF,EACpG,UAAU,IAAI,4BAA4B,0BAC1C;0DACC,QAAQ;;;;;;0DAEX,6LAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;;;;;;;kDAE1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,iBACR,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,OAAO,IAC3B,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,OAAO;;;;;;4CAGlC,WAAW,gCACV,6LAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY;;;;;;;;;;;;;+BAtBlC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAgC5B;KAlMM;uCAoMS", "debugId": null}}, {"offset": {"line": 1573, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/components/charts/PerformanceMetrics.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\n\nexport interface PerformanceMetricsData {\n  bestPerformer: {\n    name: string;\n    cagr: number;\n    absoluteReturn: number;\n  };\n  worstPerformer: {\n    name: string;\n    cagr: number;\n    absoluteReturn: number;\n  };\n  averageCagr: number;\n  volatilityRanking: Array<{\n    name: string;\n    volatility: number;\n  }>;\n}\n\ninterface PerformanceMetricsProps {\n  data: PerformanceMetricsData;\n  investmentAmount: number;\n  investmentPeriod: string;\n  title?: string;\n}\n\nconst PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({\n  data,\n  investmentAmount,\n  investmentPeriod,\n  title = 'Performance Metrics',\n}) => {\n  const MetricCard: React.FC<{\n    title: string;\n    value: string;\n    subtitle?: string;\n    color: 'green' | 'red' | 'blue' | 'purple' | 'yellow';\n    icon?: React.ReactNode;\n  }> = ({ title, value, subtitle, color, icon }) => {\n    const colorClasses = {\n      green: 'bg-green-50 border-green-200 text-green-800',\n      red: 'bg-red-50 border-red-200 text-red-800',\n      blue: 'bg-blue-50 border-blue-200 text-blue-800',\n      purple: 'bg-purple-50 border-purple-200 text-purple-800',\n      yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n    };\n\n    return (\n      <div className={`p-4 rounded-lg border ${colorClasses[color]}`}>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h4 className=\"text-sm font-medium opacity-75\">{title}</h4>\n            <p className=\"text-xl font-bold mt-1\">{value}</p>\n            {subtitle && (\n              <p className=\"text-sm opacity-75 mt-1\">{subtitle}</p>\n            )}\n          </div>\n          {icon && (\n            <div className=\"text-2xl opacity-50\">\n              {icon}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  };\n\n  const RankingList: React.FC<{\n    title: string;\n    items: Array<{ name: string; volatility: number }>;\n  }> = ({ title, items }) => (\n    <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\n      <h4 className=\"text-md font-semibold text-gray-800 mb-3\">{title}</h4>\n      <div className=\"space-y-2\">\n        {items.map((item, index) => (\n          <div key={item.name} className=\"flex justify-between items-center\">\n            <div className=\"flex items-center\">\n              <span className=\"inline-flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-xs font-medium text-gray-600 mr-3\">\n                {index + 1}\n              </span>\n              <span className=\"text-sm font-medium\">{item.name}</span>\n            </div>\n            <span className=\"text-sm text-gray-600\">\n              {formatPercentage(item.volatility)}\n            </span>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"w-full space-y-6\">\n      {title && (\n        <h3 className=\"text-lg font-semibold text-gray-800 text-center\">\n          {title}\n        </h3>\n      )}\n\n      {/* Investment Summary */}\n      <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200\">\n        <h4 className=\"text-md font-semibold text-gray-800 mb-2\">Investment Summary</h4>\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <span className=\"text-gray-600\">Investment Amount:</span>\n            <span className=\"font-semibold ml-2\">{formatCurrency(investmentAmount)}</span>\n          </div>\n          <div>\n            <span className=\"text-gray-600\">Investment Period:</span>\n            <span className=\"font-semibold ml-2\">{investmentPeriod}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Key Performance Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <MetricCard\n          title=\"Best Performer\"\n          value={data.bestPerformer.name}\n          subtitle={`${formatPercentage(data.bestPerformer.cagr)} CAGR`}\n          color=\"green\"\n          icon=\"🏆\"\n        />\n        \n        <MetricCard\n          title=\"Worst Performer\"\n          value={data.worstPerformer.name}\n          subtitle={`${formatPercentage(data.worstPerformer.cagr)} CAGR`}\n          color=\"red\"\n          icon=\"📉\"\n        />\n        \n        <MetricCard\n          title=\"Average CAGR\"\n          value={formatPercentage(data.averageCagr)}\n          subtitle=\"Across all options\"\n          color=\"blue\"\n          icon=\"📊\"\n        />\n        \n        <MetricCard\n          title=\"Most Stable\"\n          value={data.volatilityRanking[0]?.name || 'N/A'}\n          subtitle=\"Lowest volatility\"\n          color=\"purple\"\n          icon=\"🛡️\"\n        />\n      </div>\n\n      {/* Detailed Performance Comparison */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Best vs Worst Comparison */}\n        <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\n          <h4 className=\"text-md font-semibold text-gray-800 mb-3\">\n            Best vs Worst Performance\n          </h4>\n          \n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between items-center p-3 bg-green-50 rounded\">\n              <div>\n                <div className=\"font-semibold text-green-800\">\n                  🏆 {data.bestPerformer.name}\n                </div>\n                <div className=\"text-sm text-green-600\">\n                  CAGR: {formatPercentage(data.bestPerformer.cagr)}\n                </div>\n              </div>\n              <div className=\"text-right text-green-700\">\n                <div className=\"font-bold\">\n                  {formatPercentage(data.bestPerformer.absoluteReturn)}\n                </div>\n                <div className=\"text-xs\">Total Return</div>\n              </div>\n            </div>\n            \n            <div className=\"flex justify-between items-center p-3 bg-red-50 rounded\">\n              <div>\n                <div className=\"font-semibold text-red-800\">\n                  📉 {data.worstPerformer.name}\n                </div>\n                <div className=\"text-sm text-red-600\">\n                  CAGR: {formatPercentage(data.worstPerformer.cagr)}\n                </div>\n              </div>\n              <div className=\"text-right text-red-700\">\n                <div className=\"font-bold\">\n                  {formatPercentage(data.worstPerformer.absoluteReturn)}\n                </div>\n                <div className=\"text-xs\">Total Return</div>\n              </div>\n            </div>\n            \n            <div className=\"p-3 bg-blue-50 rounded\">\n              <div className=\"text-center text-blue-800\">\n                <div className=\"font-semibold\">Performance Gap</div>\n                <div className=\"text-lg font-bold\">\n                  {formatPercentage(data.bestPerformer.cagr - data.worstPerformer.cagr)}\n                </div>\n                <div className=\"text-xs\">CAGR Difference</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Volatility Ranking */}\n        <RankingList\n          title=\"Stability Ranking (Low to High Volatility)\"\n          items={data.volatilityRanking}\n        />\n      </div>\n\n      {/* Investment Insights */}\n      <div className=\"bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200\">\n        <h4 className=\"text-md font-semibold text-gray-800 mb-3 flex items-center\">\n          💡 Key Insights\n        </h4>\n        <div className=\"space-y-2 text-sm text-gray-700\">\n          <p>\n            • The best performing option ({data.bestPerformer.name}) delivered{' '}\n            <span className=\"font-semibold\">{formatPercentage(data.bestPerformer.cagr)}</span> CAGR\n          </p>\n          <p>\n            • Average performance across all options was{' '}\n            <span className=\"font-semibold\">{formatPercentage(data.averageCagr)}</span> CAGR\n          </p>\n          <p>\n            • Most stable investment was{' '}\n            <span className=\"font-semibold\">{data.volatilityRanking[0]?.name}</span> with lowest volatility\n          </p>\n          <p>\n            • Performance gap between best and worst was{' '}\n            <span className=\"font-semibold\">\n              {formatPercentage(data.bestPerformer.cagr - data.worstPerformer.cagr)}\n            </span>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceMetrics;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AA8BA,MAAM,qBAAwD,CAAC,EAC7D,IAAI,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,qBAAqB,EAC9B;IACC,MAAM,aAMD,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;QAC3C,MAAM,eAAe;YACnB,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QAEA,qBACE,6LAAC;YAAI,WAAW,CAAC,sBAAsB,EAAE,YAAY,CAAC,MAAM,EAAE;sBAC5D,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;4BACtC,0BACC,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;;;;;;;oBAG3C,sBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;IAMb;IAEA,MAAM,cAGD,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBACpB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,QAAQ;;;;;;sDAEX,6LAAC;4CAAK,WAAU;sDAAuB,KAAK,IAAI;;;;;;;;;;;;8CAElD,6LAAC;oCAAK,WAAU;8CACb,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU;;;;;;;2BAR3B,KAAK,IAAI;;;;;;;;;;;;;;;;IAgB3B,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAsB,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;0CAEvD,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;0BAM5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,OAAO,KAAK,aAAa,CAAC,IAAI;wBAC9B,UAAU,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;wBAC7D,OAAM;wBACN,MAAK;;;;;;kCAGP,6LAAC;wBACC,OAAM;wBACN,OAAO,KAAK,cAAc,CAAC,IAAI;wBAC/B,UAAU,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;wBAC9D,OAAM;wBACN,MAAK;;;;;;kCAGP,6LAAC;wBACC,OAAM;wBACN,OAAO,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,WAAW;wBACxC,UAAS;wBACT,OAAM;wBACN,MAAK;;;;;;kCAGP,6LAAC;wBACC,OAAM;wBACN,OAAO,KAAK,iBAAiB,CAAC,EAAE,EAAE,QAAQ;wBAC1C,UAAS;wBACT,OAAM;wBACN,MAAK;;;;;;;;;;;;0BAKT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;4DAA+B;4DACxC,KAAK,aAAa,CAAC,IAAI;;;;;;;kEAE7B,6LAAC;wDAAI,WAAU;;4DAAyB;4DAC/B,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,CAAC,IAAI;;;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,CAAC,cAAc;;;;;;kEAErD,6LAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;;kDAI7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;4DAA6B;4DACtC,KAAK,cAAc,CAAC,IAAI;;;;;;;kEAE9B,6LAAC;wDAAI,WAAU;;4DAAuB;4DAC7B,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,cAAc,CAAC,IAAI;;;;;;;;;;;;;0DAGpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,cAAc,CAAC,cAAc;;;;;;kEAEtD,6LAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;;kDAI7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,CAAC,IAAI,GAAG,KAAK,cAAc,CAAC,IAAI;;;;;;8DAEtE,6LAAC;oDAAI,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjC,6LAAC;wBACC,OAAM;wBACN,OAAO,KAAK,iBAAiB;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAG3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAE;oCAC8B,KAAK,aAAa,CAAC,IAAI;oCAAC;oCAAY;kDACnE,6LAAC;wCAAK,WAAU;kDAAiB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,CAAC,IAAI;;;;;;oCAAS;;;;;;;0CAEpF,6LAAC;;oCAAE;oCAC4C;kDAC7C,6LAAC;wCAAK,WAAU;kDAAiB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,WAAW;;;;;;oCAAS;;;;;;;0CAE7E,6LAAC;;oCAAE;oCAC4B;kDAC7B,6LAAC;wCAAK,WAAU;kDAAiB,KAAK,iBAAiB,CAAC,EAAE,EAAE;;;;;;oCAAY;;;;;;;0CAE1E,6LAAC;;oCAAE;oCAC4C;kDAC7C,6LAAC;wCAAK,WAAU;kDACb,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,CAAC,IAAI,GAAG,KAAK,cAAc,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF;KArNM;uCAuNS", "debugId": null}}, {"offset": {"line": 2185, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/components/AnalysisResults.tsx"], "sourcesContent": ["'use client';\n\nimport { InvestmentResult, ComparisonResult } from '@/lib/types';\nimport ComparisonTable from '@/components/tables/ComparisonTable';\nimport TimeSeriesChart from '@/components/charts/TimeSeriesChart';\nimport ComparisonBarChart from '@/components/charts/ComparisonBarChart';\nimport PerformanceMetrics from '@/components/charts/PerformanceMetrics';\nimport { formatCurrency, formatPercentage } from '@/lib/utils';\n\n// Share on X functionality\nfunction shareOnX(investmentResult: InvestmentResult, comparisonSummary: any) {\n  const stockSymbol = investmentResult.scenario.stockSymbol;\n  const cagr = formatPercentage(investmentResult.annualizedReturn);\n  const totalReturn = formatCurrency(investmentResult.totalReturn);\n  const period = `${investmentResult.scenario.startDate.toLocaleDateString()} - ${investmentResult.scenario.endDate.toLocaleDateString()}`;\n\n  // Generate compelling insights for social sharing\n  const insights = [];\n\n  if (comparisonSummary?.investment?.rank === 1) {\n    insights.push(`🎉 ${stockSymbol} outperformed ALL benchmarks!`);\n  } else {\n    insights.push(`📊 ${stockSymbol} ranked #${comparisonSummary?.investment?.rank || 'N/A'} in performance`);\n  }\n\n  if (investmentResult.annualizedReturn > 15) {\n    insights.push(`🚀 Stellar ${cagr} CAGR!`);\n  } else if (investmentResult.annualizedReturn > 10) {\n    insights.push(`📈 Strong ${cagr} CAGR`);\n  } else if (investmentResult.annualizedReturn > 0) {\n    insights.push(`💰 Positive ${cagr} returns`);\n  }\n\n  // Create shareable text\n  const shareText = `💡 Investment Analysis Results:\n\n${insights.join(' ')}\n\n📊 ${stockSymbol}: ${cagr} CAGR\n💵 Total Return: ${totalReturn}\n📅 Period: ${period}\n\nAnalyzed with real market data 📈\n\n#InvestmentAnalysis #StockMarket #IndianStocks #WealthBuilding`;\n\n  // Create X share URL\n  const tweetUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(window.location.href)}`;\n\n  // Open in new window\n  window.open(tweetUrl, '_blank', 'width=550,height=420');\n}\n\ninterface AnalysisResultsProps {\n  data: {\n    investmentResult: InvestmentResult;\n    comparisonResult: ComparisonResult;\n    comparisonSummary: any;\n    chartData: any;\n  };\n}\n\nexport function AnalysisResults({ data }: AnalysisResultsProps) {\n  const { investmentResult, comparisonSummary, chartData } = data;\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Investment Summary Cards with Glassmorphism */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"glass-card p-6 float-animation\">\n          <h3 className=\"text-sm font-medium text-blue-200 mb-2\">Initial Investment</h3>\n          <p className=\"text-3xl font-bold text-white\">\n            {formatCurrency(investmentResult.initialValue)}\n          </p>\n        </div>\n\n        <div className=\"glass-card p-6 float-animation\" style={{animationDelay: '0.2s'}}>\n          <h3 className=\"text-sm font-medium text-blue-200 mb-2\">Current Value</h3>\n          <p className=\"text-3xl font-bold text-green-300\">\n            {formatCurrency(investmentResult.currentValue)}\n          </p>\n        </div>\n\n        <div className=\"glass-card p-6 float-animation\" style={{animationDelay: '0.4s'}}>\n          <h3 className=\"text-sm font-medium text-blue-200 mb-2\">Total Return</h3>\n          <p className={`text-3xl font-bold ${investmentResult.totalReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>\n            {formatCurrency(investmentResult.totalReturn)}\n          </p>\n        </div>\n\n        <div className=\"glass-card p-6 float-animation\" style={{animationDelay: '0.6s'}}>\n          <h3 className=\"text-sm font-medium text-blue-200 mb-2\">CAGR</h3>\n          <p className={`text-3xl font-bold ${investmentResult.cagr >= 0 ? 'text-green-300' : 'text-red-300'}`}>\n            {formatPercentage(investmentResult.cagr)}\n          </p>\n        </div>\n      </div>\n\n      {/* Investment Details with Modern Styling */}\n      <div className=\"glass-card p-8\">\n        <h3 className=\"text-2xl font-bold text-white mb-6\">Investment Details</h3>\n        <div className=\"mb-6 glass-card p-4 border border-green-400/30 bg-green-500/10\">\n          <div className=\"flex items-center gap-3 text-green-200\">\n            <span className=\"text-green-400 text-lg\">✅</span>\n            <span className=\"font-semibold\">Real Market Data:</span>\n            <span>Stock prices from Angel One API</span>\n          </div>\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-blue-200\">Stock Symbol:</span>\n              <span className=\"font-bold text-white text-lg\">{investmentResult.scenario.stockSymbol}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-blue-200\">Investment Period:</span>\n              <span className=\"font-semibold text-white\">\n                {new Date(investmentResult.scenario.startDate).toLocaleDateString()} - {new Date(investmentResult.scenario.endDate).toLocaleDateString()}\n              </span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-blue-200\">Absolute Return:</span>\n              <span className={`font-bold text-lg ${investmentResult.absoluteReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>\n                {formatPercentage(investmentResult.absoluteReturn)}\n              </span>\n            </div>\n          </div>\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-blue-200\">Annualized Return:</span>\n              <span className={`font-bold text-lg ${investmentResult.annualizedReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>\n                {formatPercentage(investmentResult.annualizedReturn)}\n              </span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-blue-200\">Investment Amount:</span>\n              <span className=\"font-bold text-white text-lg\">{formatCurrency(investmentResult.scenario.investmentAmount)}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-blue-200\">Profit/Loss:</span>\n              <span className={`font-bold text-lg ${investmentResult.totalReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>\n                {investmentResult.totalReturn >= 0 ? '+' : ''}{formatCurrency(investmentResult.totalReturn)}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Insights with Modern Styling */}\n      {comparisonSummary && (\n        <div className=\"glass-card p-8\">\n          <h3 className=\"text-2xl font-bold text-white mb-6\">Performance Insights</h3>\n          <div className=\"space-y-4\">\n            {comparisonSummary.insights?.map((insight: string, index: number) => (\n              <div key={index} className=\"flex items-start space-x-4 glass-card p-4 hover:scale-105 transition-transform duration-300\">\n                <div className=\"w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mt-2 flex-shrink-0\"></div>\n                <p className=\"text-white font-medium\">{insight}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Charts Section with Modern Styling */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Time Series Chart */}\n        {chartData?.timeSeriesData && (\n          <div className=\"glass-card p-8\">\n            <h3 className=\"text-2xl font-bold text-white mb-6\">📈 Investment Growth Over Time</h3>\n            <TimeSeriesChart data={chartData.timeSeriesData} />\n          </div>\n        )}\n\n        {/* Comparison Bar Chart */}\n        {chartData?.barChartData && (\n          <div className=\"glass-card p-8\">\n            <h3 className=\"text-2xl font-bold text-white mb-6\">📊 Performance Comparison</h3>\n            <ComparisonBarChart data={chartData.barChartData} />\n          </div>\n        )}\n      </div>\n\n      {/* Performance Metrics Component */}\n      {comparisonSummary && (() => {\n        // Create array of all investments including the main investment\n        const allInvestments = [\n          {\n            name: comparisonSummary.investment.name,\n            cagr: comparisonSummary.investment.cagr,\n            absoluteReturn: comparisonSummary.investment.absoluteReturn,\n          },\n          ...comparisonSummary.benchmarks.map(b => ({\n            name: b.name,\n            cagr: b.cagr,\n            absoluteReturn: b.absoluteReturn,\n          }))\n        ];\n\n        // Find actual best and worst performers by CAGR\n        const bestPerformer = allInvestments.reduce((best, current) =>\n          current.cagr > best.cagr ? current : best\n        );\n\n        const worstPerformer = allInvestments.reduce((worst, current) =>\n          current.cagr < worst.cagr ? current : worst\n        );\n\n        return (\n          <div className=\"glass-card p-8\">\n            <h3 className=\"text-2xl font-bold text-white mb-6\">📊 Detailed Performance Metrics</h3>\n            <PerformanceMetrics\n              data={{\n                bestPerformer,\n                worstPerformer,\n                averageCagr: allInvestments.reduce((sum, inv) => sum + inv.cagr, 0) / allInvestments.length,\n                volatilityRanking: [\n                  { name: 'Bank Nifty', volatility: 15 },\n                  { name: 'Nifty IT', volatility: 18 },\n                  { name: 'Nifty 50', volatility: 20 },\n                  { name: comparisonSummary.investment.name, volatility: 25 },\n                ].sort((a, b) => a.volatility - b.volatility),\n              }}\n              investmentAmount={investmentResult.scenario.investmentAmount}\n              investmentPeriod={`${new Date(investmentResult.scenario.startDate).toLocaleDateString()} - ${new Date(investmentResult.scenario.endDate).toLocaleDateString()}`}\n            />\n          </div>\n        );\n      })()}\n\n      {/* Comparison Table with Modern Styling */}\n      {comparisonSummary && (\n        <div className=\"glass-card p-8\">\n          <h3 className=\"text-2xl font-bold text-white mb-6\">📊 Benchmark Comparison</h3>\n          <div className=\"mb-6 grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"glass-card p-4 border border-green-400/30 bg-green-500/10\">\n              <div className=\"flex items-center gap-3 text-green-200\">\n                <span className=\"text-green-400 text-lg\">✅</span>\n                <span className=\"font-semibold\">Real Market Data:</span>\n                <span>All indices from Angel One API</span>\n              </div>\n            </div>\n            <div className=\"glass-card p-4 border border-blue-400/30 bg-blue-500/10\">\n              <div className=\"flex items-center gap-3 text-blue-200\">\n                <span className=\"text-blue-400 text-lg\">📊</span>\n                <span className=\"font-semibold\">Live Benchmarks:</span>\n                <span>Nifty 50, Bank Nifty, Nifty IT</span>\n              </div>\n            </div>\n            <div className=\"glass-card p-4 border border-purple-400/30 bg-purple-500/10\">\n              <div className=\"flex items-center gap-3 text-purple-200\">\n                <span className=\"text-purple-400 text-lg\">🎯</span>\n                <span className=\"font-semibold\">Methodology:</span>\n                <span>Consistent calculation across all assets</span>\n              </div>\n            </div>\n          </div>\n          <ComparisonTable\n            data={[comparisonSummary.investment, ...comparisonSummary.benchmarks]}\n            investmentName={comparisonSummary.investment.name}\n            title=\"Investment vs Benchmarks\"\n            showOutperformance={true}\n          />\n        </div>\n      )}\n\n      {/* Benchmark Details */}\n      {comparisonSummary && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">Benchmark Performance</h3>\n          <div className=\"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800\">\n            <div className=\"flex items-center gap-2 text-sm text-yellow-800 dark:text-yellow-200\">\n              <span className=\"text-yellow-600\">⚠️</span>\n              <span className=\"font-medium\">Note:</span>\n              <span>Benchmark data uses consistent calculation methodology as comparison table</span>\n            </div>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {comparisonSummary.benchmarks.map((benchmark, index) => (\n              <div key={benchmark.name} className=\"p-4 border border-gray-200 dark:border-gray-700 rounded-lg\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                  {benchmark.name}\n                </h4>\n                <div className=\"space-y-1 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">Initial:</span>\n                    <span className=\"text-gray-900 dark:text-white\">{formatCurrency(benchmark.initialValue)}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">Current:</span>\n                    <span className=\"text-gray-900 dark:text-white\">{formatCurrency(benchmark.currentValue)}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">CAGR:</span>\n                    <span className={`${benchmark.cagr >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\n                      {formatPercentage(benchmark.cagr)}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">Return:</span>\n                    <span className={`${benchmark.absoluteReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\n                      {formatPercentage(benchmark.absoluteReturn)}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Share on X Button */}\n      <div className=\"flex justify-center\">\n        <button\n          onClick={() => shareOnX(investmentResult, comparisonSummary)}\n          className=\"btn-success px-8 py-4 text-lg font-bold rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n          </svg>\n          <span>Share on X</span>\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AASA,2BAA2B;AAC3B,SAAS,SAAS,gBAAkC,EAAE,iBAAsB;IAC1E,MAAM,cAAc,iBAAiB,QAAQ,CAAC,WAAW;IACzD,MAAM,OAAO,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,gBAAgB;IAC/D,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,WAAW;IAC/D,MAAM,SAAS,GAAG,iBAAiB,QAAQ,CAAC,SAAS,CAAC,kBAAkB,GAAG,GAAG,EAAE,iBAAiB,QAAQ,CAAC,OAAO,CAAC,kBAAkB,IAAI;IAExI,kDAAkD;IAClD,MAAM,WAAW,EAAE;IAEnB,IAAI,mBAAmB,YAAY,SAAS,GAAG;QAC7C,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,YAAY,6BAA6B,CAAC;IAChE,OAAO;QACL,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,YAAY,SAAS,EAAE,mBAAmB,YAAY,QAAQ,MAAM,eAAe,CAAC;IAC1G;IAEA,IAAI,iBAAiB,gBAAgB,GAAG,IAAI;QAC1C,SAAS,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;IAC1C,OAAO,IAAI,iBAAiB,gBAAgB,GAAG,IAAI;QACjD,SAAS,IAAI,CAAC,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC;IACxC,OAAO,IAAI,iBAAiB,gBAAgB,GAAG,GAAG;QAChD,SAAS,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,QAAQ,CAAC;IAC7C;IAEA,wBAAwB;IACxB,MAAM,YAAY,CAAC;;AAErB,EAAE,SAAS,IAAI,CAAC,KAAK;;GAElB,EAAE,YAAY,EAAE,EAAE,KAAK;iBACT,EAAE,YAAY;WACpB,EAAE,OAAO;;;;8DAI0C,CAAC;IAE7D,qBAAqB;IACrB,MAAM,WAAW,CAAC,sCAAsC,EAAE,mBAAmB,WAAW,KAAK,EAAE,mBAAmB,OAAO,QAAQ,CAAC,IAAI,GAAG;IAEzI,qBAAqB;IACrB,OAAO,IAAI,CAAC,UAAU,UAAU;AAClC;AAWO,SAAS,gBAAgB,EAAE,IAAI,EAAwB;IAC5D,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,SAAS,EAAE,GAAG;IAE3D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,YAAY;;;;;;;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAC,gBAAgB;wBAAM;;0CAC5E,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,YAAY;;;;;;;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAC,gBAAgB;wBAAM;;0CAC5E,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAW,CAAC,mBAAmB,EAAE,iBAAiB,WAAW,IAAI,IAAI,mBAAmB,gBAAgB;0CACxG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,WAAW;;;;;;;;;;;;kCAIhD,6LAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAC,gBAAgB;wBAAM;;0CAC5E,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAW,CAAC,mBAAmB,EAAE,iBAAiB,IAAI,IAAI,IAAI,mBAAmB,gBAAgB;0CACjG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;0BAM7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAyB;;;;;;8CACzC,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAgC,iBAAiB,QAAQ,CAAC,WAAW;;;;;;;;;;;;kDAEvF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDACb,IAAI,KAAK,iBAAiB,QAAQ,CAAC,SAAS,EAAE,kBAAkB;oDAAG;oDAAI,IAAI,KAAK,iBAAiB,QAAQ,CAAC,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;kDAG1I,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,cAAc,IAAI,IAAI,mBAAmB,gBAAgB;0DAC7G,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;0CAIvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,gBAAgB,IAAI,IAAI,mBAAmB,gBAAgB;0DAC/G,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,gBAAgB;;;;;;;;;;;;kDAGvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAgC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,QAAQ,CAAC,gBAAgB;;;;;;;;;;;;kDAE3G,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,WAAW,IAAI,IAAI,mBAAmB,gBAAgB;;oDAC1G,iBAAiB,WAAW,IAAI,IAAI,MAAM;oDAAI,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnG,mCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,QAAQ,EAAE,IAAI,CAAC,SAAiB,sBACjD,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;+BAF/B;;;;;;;;;;;;;;;;0BAUlB,6LAAC;gBAAI,WAAU;;oBAEZ,WAAW,gCACV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC,kJAAA,CAAA,UAAe;gCAAC,MAAM,UAAU,cAAc;;;;;;;;;;;;oBAKlD,WAAW,8BACV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC,qJAAA,CAAA,UAAkB;gCAAC,MAAM,UAAU,YAAY;;;;;;;;;;;;;;;;;;YAMrD,qBAAqB,CAAC;gBACrB,gEAAgE;gBAChE,MAAM,iBAAiB;oBACrB;wBACE,MAAM,kBAAkB,UAAU,CAAC,IAAI;wBACvC,MAAM,kBAAkB,UAAU,CAAC,IAAI;wBACvC,gBAAgB,kBAAkB,UAAU,CAAC,cAAc;oBAC7D;uBACG,kBAAkB,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;4BACxC,MAAM,EAAE,IAAI;4BACZ,MAAM,EAAE,IAAI;4BACZ,gBAAgB,EAAE,cAAc;wBAClC,CAAC;iBACF;gBAED,gDAAgD;gBAChD,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAC,MAAM,UACjD,QAAQ,IAAI,GAAG,KAAK,IAAI,GAAG,UAAU;gBAGvC,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAC,OAAO,UACnD,QAAQ,IAAI,GAAG,MAAM,IAAI,GAAG,UAAU;gBAGxC,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC,qJAAA,CAAA,UAAkB;4BACjB,MAAM;gCACJ;gCACA;gCACA,aAAa,eAAe,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,IAAI,EAAE,KAAK,eAAe,MAAM;gCAC3F,mBAAmB;oCACjB;wCAAE,MAAM;wCAAc,YAAY;oCAAG;oCACrC;wCAAE,MAAM;wCAAY,YAAY;oCAAG;oCACnC;wCAAE,MAAM;wCAAY,YAAY;oCAAG;oCACnC;wCAAE,MAAM,kBAAkB,UAAU,CAAC,IAAI;wCAAE,YAAY;oCAAG;iCAC3D,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;4BAC9C;4BACA,kBAAkB,iBAAiB,QAAQ,CAAC,gBAAgB;4BAC5D,kBAAkB,GAAG,IAAI,KAAK,iBAAiB,QAAQ,CAAC,SAAS,EAAE,kBAAkB,GAAG,GAAG,EAAE,IAAI,KAAK,iBAAiB,QAAQ,CAAC,OAAO,EAAE,kBAAkB,IAAI;;;;;;;;;;;;YAIvK,CAAC;YAGA,mCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;sDACzC,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;sDAC1C,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAIZ,6LAAC,kJAAA,CAAA,UAAe;wBACd,MAAM;4BAAC,kBAAkB,UAAU;+BAAK,kBAAkB,UAAU;yBAAC;wBACrE,gBAAgB,kBAAkB,UAAU,CAAC,IAAI;wBACjD,OAAM;wBACN,oBAAoB;;;;;;;;;;;;YAMzB,mCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCACzE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,6LAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC5C,6LAAC;gCAAyB,WAAU;;kDAClC,6LAAC;wCAAG,WAAU;kDACX,UAAU,IAAI;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAU;kEAAiC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,YAAY;;;;;;;;;;;;0DAExF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAU;kEAAiC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,YAAY;;;;;;;;;;;;0DAExF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAW,GAAG,UAAU,IAAI,IAAI,IAAI,uCAAuC,kCAAkC;kEAChH,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,IAAI;;;;;;;;;;;;0DAGpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAW,GAAG,UAAU,cAAc,IAAI,IAAI,uCAAuC,kCAAkC;kEAC1H,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,cAAc;;;;;;;;;;;;;;;;;;;+BAtBxC,UAAU,IAAI;;;;;;;;;;;;;;;;0BAiChC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS,IAAM,SAAS,kBAAkB;oBAC1C,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAe,SAAQ;sCACnD,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;sCAEV,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;KAtQgB", "debugId": null}}, {"offset": {"line": 3176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/whatif/whatif/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { InvestmentAnalysisForm } from '@/components/InvestmentAnalysisForm';\nimport { AnalysisResults } from '@/components/AnalysisResults';\nimport { InvestmentScenario, InvestmentResult, ComparisonResult } from '@/lib/types';\n\ninterface AnalysisData {\n  investmentResult: InvestmentResult;\n  comparisonResult: ComparisonResult;\n  comparisonSummary: any;\n  chartData: any;\n}\n\nexport default function Home() {\n  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleAnalysisSubmit = async (scenario: Omit<InvestmentScenario, 'id' | 'createdAt'>) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(scenario),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to analyze investment');\n      }\n\n      const data = await response.json();\n      setAnalysisData(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setAnalysisData(null);\n    setError(null);\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Killer Header with Glassmorphism */}\n        <div className=\"text-center mb-12 float-animation\">\n          <div className=\"glass-card p-8 mb-8 max-w-4xl mx-auto\">\n            <h1 className=\"text-6xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent mb-6\">\n              What If Investment Analyzer\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed\">\n              🚀 Analyze your investment scenarios with <span className=\"font-semibold text-blue-200\">real market data</span> and\n              <span className=\"font-semibold text-purple-200\"> intelligent benchmark comparisons</span>\n            </p>\n            <div className=\"flex justify-center mt-6 space-x-4\">\n              <div className=\"flex items-center space-x-2 text-green-200\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></span>\n                <span className=\"text-sm\">Live Market Data</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-blue-200\">\n                <span className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></span>\n                <span className=\"text-sm\">Real-time Analysis</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-purple-200\">\n                <span className=\"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"></span>\n                <span className=\"text-sm\">Smart Insights</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content with Killer UI */}\n        <div className=\"max-w-7xl mx-auto\">\n          {!analysisData ? (\n            /* Input Form with Glassmorphism */\n            <div className=\"glass-card p-8 pulse-glow\">\n              <InvestmentAnalysisForm\n                onSubmit={handleAnalysisSubmit}\n                loading={loading}\n                error={error}\n              />\n            </div>\n          ) : (\n            /* Results Display with Modern Styling */\n            <div className=\"space-y-8\">\n              <div className=\"glass-card p-6 flex justify-between items-center\">\n                <h2 className=\"text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">\n                  Analysis Results\n                </h2>\n                <button\n                  onClick={handleReset}\n                  className=\"btn-gradient px-6 py-3 rounded-xl font-semibold transition-all duration-300\"\n                >\n                  New Analysis\n                </button>\n              </div>\n\n              <div className=\"glass-card p-8\">\n                <AnalysisResults data={analysisData} />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAce,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,uBAAuB,OAAO;QAClC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgG;;;;;;0CAG9G,6LAAC;gCAAE,WAAU;;oCAA0D;kDAC3B,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;oCAAuB;kDAC/G,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOlC,6LAAC;oBAAI,WAAU;8BACZ,CAAC,eACA,iCAAiC,iBACjC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+IAAA,CAAA,yBAAsB;4BACrB,UAAU;4BACV,SAAS;4BACT,OAAO;;;;;;;;;;+BAIX,uCAAuC,iBACvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2F;;;;;;kDAGzG,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wIAAA,CAAA,kBAAe;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;GArGwB;KAAA", "debugId": null}}]}