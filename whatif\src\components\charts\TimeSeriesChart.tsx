'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { formatCurrency, formatPercentage } from '@/lib/utils';
import { UI_CONFIG } from '@/lib/config';

export interface TimeSeriesDataPoint {
  date: string;
  investment: number;
  gold: number;
  fd: number;
  nifty: number;
}

interface TimeSeriesChartProps {
  data: TimeSeriesDataPoint[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  investmentLabel?: string;
}

const TimeSeriesChart: React.FC<TimeSeriesChartProps> = ({
  data,
  title = 'Investment Performance Comparison',
  height = UI_CONFIG.charts.defaultHeight,
  showLegend = true,
  investmentLabel = 'Your Investment',
}) => {
  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-300 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`Date: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Format Y-axis values
  const formatYAxis = (value: number) => {
    if (value >= 100000) {
      return `₹${(value / 100000).toFixed(1)}L`;
    } else if (value >= 1000) {
      return `₹${(value / 1000).toFixed(1)}K`;
    }
    return `₹${value}`;
  };

  // Format X-axis dates
  const formatXAxis = (tickItem: string) => {
    const date = new Date(tickItem);
    return date.toLocaleDateString('en-IN', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
          {title}
        </h3>
      )}
      
      <ResponsiveContainer width="100%" height={height}>
        <LineChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          
          <XAxis
            dataKey="date"
            tickFormatter={formatXAxis}
            stroke="#666"
            fontSize={12}
            tick={{ fill: '#666' }}
          />
          
          <YAxis
            tickFormatter={formatYAxis}
            stroke="#666"
            fontSize={12}
            tick={{ fill: '#666' }}
          />
          
          <Tooltip content={<CustomTooltip />} />
          
          {showLegend && (
            <Legend
              wrapperStyle={{
                paddingTop: '20px',
                fontSize: '14px',
              }}
            />
          )}
          
          <Line
            type="monotone"
            dataKey="investment"
            stroke={UI_CONFIG.charts.colors.investment}
            strokeWidth={3}
            dot={{ fill: UI_CONFIG.charts.colors.investment, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: UI_CONFIG.charts.colors.investment, strokeWidth: 2 }}
            name={investmentLabel}
          />
          
          <Line
            type="monotone"
            dataKey="nifty"
            stroke={UI_CONFIG.charts.colors.nifty}
            strokeWidth={2}
            dot={{ fill: UI_CONFIG.charts.colors.nifty, strokeWidth: 1, r: 3 }}
            name="Nifty 50"
          />
          
          <Line
            type="monotone"
            dataKey="gold"
            stroke={UI_CONFIG.charts.colors.gold}
            strokeWidth={2}
            dot={{ fill: UI_CONFIG.charts.colors.gold, strokeWidth: 1, r: 3 }}
            name="Gold"
          />
          
          <Line
            type="monotone"
            dataKey="fd"
            stroke={UI_CONFIG.charts.colors.fd}
            strokeWidth={2}
            dot={{ fill: UI_CONFIG.charts.colors.fd, strokeWidth: 1, r: 3 }}
            name="Fixed Deposit"
          />
        </LineChart>
      </ResponsiveContainer>
      
      {/* Performance Summary */}
      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {data.length > 0 && (
          <>
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="font-semibold text-blue-800">Your Investment</div>
              <div className="text-blue-600">
                {formatCurrency(data[data.length - 1].investment)}
              </div>
            </div>
            
            <div className="text-center p-2 bg-purple-50 rounded">
              <div className="font-semibold text-purple-800">Nifty 50</div>
              <div className="text-purple-600">
                {formatCurrency(data[data.length - 1].nifty)}
              </div>
            </div>
            
            <div className="text-center p-2 bg-yellow-50 rounded">
              <div className="font-semibold text-yellow-800">Gold</div>
              <div className="text-yellow-600">
                {formatCurrency(data[data.length - 1].gold)}
              </div>
            </div>
            
            <div className="text-center p-2 bg-green-50 rounded">
              <div className="font-semibold text-green-800">Fixed Deposit</div>
              <div className="text-green-600">
                {formatCurrency(data[data.length - 1].fd)}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TimeSeriesChart;
