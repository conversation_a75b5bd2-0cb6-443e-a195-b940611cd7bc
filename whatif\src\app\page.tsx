'use client';

import { useState } from 'react';
import { InvestmentAnalysisForm } from '@/components/InvestmentAnalysisForm';
import { AnalysisResults } from '@/components/AnalysisResults';
import { InvestmentScenario, InvestmentResult, ComparisonResult } from '@/lib/types';

interface AnalysisData {
  investmentResult: InvestmentResult;
  comparisonResult: ComparisonResult;
  comparisonSummary: any;
  chartData: any;
}

export default function Home() {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAnalysisSubmit = async (scenario: Omit<InvestmentScenario, 'id' | 'createdAt'>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/analyze-mock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scenario),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze investment');
      }

      const data = await response.json();
      setAnalysisData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setAnalysisData(null);
    setError(null);
  };

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Killer Header with Glassmorphism */}
        <div className="text-center mb-12 float-animation">
          <div className="glass-card p-8 mb-8 max-w-4xl mx-auto">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent mb-6">
              What If Investment Analyzer
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
              🚀 Analyze your investment scenarios with <span className="font-semibold text-blue-200">real market data</span> and
              <span className="font-semibold text-purple-200"> intelligent benchmark comparisons</span>
            </p>
            <div className="flex justify-center mt-6 space-x-4">
              <div className="flex items-center space-x-2 text-green-200">
                <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                <span className="text-sm">Live Market Data</span>
              </div>
              <div className="flex items-center space-x-2 text-blue-200">
                <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
                <span className="text-sm">Real-time Analysis</span>
              </div>
              <div className="flex items-center space-x-2 text-purple-200">
                <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></span>
                <span className="text-sm">Smart Insights</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content with Killer UI */}
        <div className="max-w-7xl mx-auto">
          {!analysisData ? (
            /* Input Form with Glassmorphism */
            <div className="glass-card p-8 pulse-glow">
              <InvestmentAnalysisForm
                onSubmit={handleAnalysisSubmit}
                loading={loading}
                error={error}
              />
            </div>
          ) : (
            /* Results Display with Modern Styling */
            <div className="space-y-8">
              <div className="glass-card p-6 flex justify-between items-center">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  Analysis Results
                </h2>
                <button
                  onClick={handleReset}
                  className="btn-gradient px-6 py-3 rounded-xl font-semibold transition-all duration-300"
                >
                  New Analysis
                </button>
              </div>

              <div className="glass-card p-8">
                <AnalysisResults data={analysisData} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
