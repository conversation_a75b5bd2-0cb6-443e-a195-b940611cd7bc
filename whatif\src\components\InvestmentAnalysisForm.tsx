dfs'use client';

import { useState } from 'react';
import { InvestmentScenario } from '@/lib/types';

interface InvestmentAnalysisFormProps {
  onSubmit: (scenario: Omit<InvestmentScenario, 'id' | 'createdAt'>) => void;
  loading: boolean;
  error: string | null;
}

// Common Indian stocks for quick selection
const POPULAR_STOCKS = [
  { symbol: 'SBIN-EQ', name: 'State Bank of India', token: '3045' },
  { symbol: 'RELIANCE-EQ', name: 'Reliance Industries', token: '2885' },
  { symbol: 'TCS-EQ', name: 'Tata Consultancy Services', token: '11536' },
  { symbol: 'INFY-EQ', name: 'Infosys Limited', token: '1594' },
  { symbol: 'HDFCBANK-EQ', name: 'HDFC Bank', token: '1333' },
  { symbol: 'ICICIBANK-EQ', name: 'ICICI Bank', token: '4963' },
  { symbol: 'BHARTIARTL-EQ', name: '<PERSON><PERSON><PERSON> Airtel', token: '10604' },
  { symbol: 'ITC-EQ', name: 'ITC Limited', token: '424' },
  { symbol: 'KOTAKBANK-EQ', name: 'Kotak Mahindra Bank', token: '1922' },
  { symbol: 'LT-EQ', name: 'Larsen & Toubro', token: '11483' },
];

export function InvestmentAnalysisForm({ onSubmit, loading, error }: InvestmentAnalysisFormProps) {
  const [formData, setFormData] = useState({
    stockSymbol: '',
    customStock: '',
    investmentAmount: '',
    startDate: '',
    endDate: new Date().toISOString().split('T')[0], // Today's date
  });

  const [useCustomStock, setUseCustomStock] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const stockSymbol = useCustomStock ? formData.customStock : formData.stockSymbol;
    
    if (!stockSymbol || !formData.investmentAmount || !formData.startDate) {
      return;
    }

    onSubmit({
      stockSymbol,
      investmentAmount: parseFloat(formData.investmentAmount),
      startDate: new Date(formData.startDate),
      endDate: new Date(formData.endDate),
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Stock Selection with Modern Styling */}
        <div className="space-y-6">
          <div>
            <label className="block text-lg font-semibold text-white mb-4">
              Stock Selection
            </label>
            <div className="space-y-4">
              <div>
                <label className="flex items-center space-x-3 glass-card p-4 cursor-pointer transition-all duration-300 hover:scale-105">
                  <input
                    type="radio"
                    checked={!useCustomStock}
                    onChange={() => setUseCustomStock(false)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <span className="text-white font-medium">Popular Stocks</span>
                </label>
                {!useCustomStock && (
                  <select
                    value={formData.stockSymbol}
                    onChange={(e) => handleInputChange('stockSymbol', e.target.value)}
                    className="mt-3 w-full modern-input px-4 py-3 text-white"
                    required={!useCustomStock}
                  >
                    <option value="">Select a stock...</option>
                    {POPULAR_STOCKS.map((stock) => (
                      <option key={stock.symbol} value={stock.symbol} className="bg-gray-800 text-white">
                        {stock.name} ({stock.symbol})
                      </option>
                    ))}
                  </select>
                )}
                {!useCustomStock && formData.stockSymbol && (
                  <div className="mt-2 text-sm text-blue-200">
                    Selected: <strong>{POPULAR_STOCKS.find(s => s.symbol === formData.stockSymbol)?.name}</strong>
                  </div>
                )}
              </div>
              
              <div>
                <label className="flex items-center space-x-3 glass-card p-4 cursor-pointer transition-all duration-300 hover:scale-105">
                  <input
                    type="radio"
                    checked={useCustomStock}
                    onChange={() => setUseCustomStock(true)}
                    className="w-4 h-4 text-blue-600"
                  />
                  <span className="text-white font-medium">Custom Stock Symbol</span>
                </label>
                {useCustomStock && (
                  <input
                    type="text"
                    value={formData.customStock}
                    onChange={(e) => handleInputChange('customStock', e.target.value)}
                    placeholder="e.g., WIPRO-EQ"
                    className="mt-3 w-full modern-input px-4 py-3 text-white"
                    required={useCustomStock}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Investment Amount with Modern Styling */}
        <div>
          <label htmlFor="investmentAmount" className="block text-lg font-semibold text-white mb-4">
            Investment Amount (₹)
          </label>
          <input
            type="number"
            id="investmentAmount"
            value={formData.investmentAmount}
            onChange={(e) => handleInputChange('investmentAmount', e.target.value)}
            placeholder="e.g., 100000"
            min="1000"
            step="1000"
            className="w-full modern-input px-4 py-3 text-white text-lg"
            required
          />
          <p className="mt-2 text-sm text-blue-200">
            Minimum investment: ₹1,000
          </p>
        </div>

        {/* Start Date with Modern Styling */}
        <div>
          <label htmlFor="startDate" className="block text-lg font-semibold text-white mb-4">
            Investment Start Date
          </label>
          <input
            type="date"
            id="startDate"
            value={formData.startDate}
            onChange={(e) => handleInputChange('startDate', e.target.value)}
            max={new Date().toISOString().split('T')[0]}
            className="w-full modern-input px-4 py-3 text-white"
            required
          />
        </div>

        {/* End Date with Modern Styling */}
        <div>
          <label htmlFor="endDate" className="block text-lg font-semibold text-white mb-4">
            Analysis End Date
          </label>
          <input
            type="date"
            id="endDate"
            value={formData.endDate}
            onChange={(e) => handleInputChange('endDate', e.target.value)}
            max={new Date().toISOString().split('T')[0]}
            className="w-full modern-input px-4 py-3 text-white"
            required
          />
          <p className="mt-2 text-sm text-blue-200">
            Default: Today's date
          </p>
        </div>
      </div>

      {/* Error Display with Modern Styling */}
      {error && (
        <div className="glass-card p-4 border border-red-400/30 bg-red-500/10">
          <p className="text-red-200 font-medium">⚠️ {error}</p>
        </div>
      )}

      {/* Submit Button with Killer Styling */}
      <div className="flex justify-center">
        <button
          type="submit"
          disabled={loading}
          className="btn-gradient px-12 py-4 text-lg font-bold rounded-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105"
        >
          {loading ? (
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              <span>🚀 Analyzing...</span>
            </div>
          ) : (
            '🎯 Analyze Investment'
          )}
        </button>
      </div>

      {/* Date Range Information with Glassmorphism */}
      <div className="mt-8 glass-card p-6 border border-blue-400/30">
        <h4 className="text-lg font-semibold text-blue-200 mb-4">📅 Date Range Guidelines:</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-100">
          <div>• <strong className="text-blue-200">Minimum:</strong> 30 days for meaningful analysis</div>
          <div>• <strong className="text-blue-200">Available from:</strong> January 1, 2018 onwards</div>
          <div>• <strong className="text-blue-200">Large ranges:</strong> Automatically chunked for processing</div>
          <div>• <strong className="text-blue-200">Recommended:</strong> 1-7 years for best insights</div>
        </div>
      </div>

      {/* Quick Examples with Modern Styling */}
      <div className="mt-6 glass-card p-6">
        <h4 className="text-lg font-semibold text-white mb-4">💡 Quick Examples:</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-white/80">
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            <span>₹1,00,000 in SBIN from Jan 2023</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
            <span>₹50,000 in TCS from Jan 2022</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
            <span>₹2,00,000 in RELIANCE from Jan 2021</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-pink-400 rounded-full"></span>
            <span>₹75,000 in INFY from Jan 2020</span>
          </div>
        </div>
      </div>
    </form>
  );
}
