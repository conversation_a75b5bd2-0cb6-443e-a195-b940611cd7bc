// Configuration for the What If investment analysis tool

export const APP_CONFIG = {
  name: 'What If',
  description: 'Investment Analysis Tool for Indian Equities',
  version: '1.0.0',
  author: 'What If Team',
} as const;

export const API_CONFIG = {
  angelOne: {
    baseUrl: 'https://apiconnect.angelone.in',
    version: 'v1',
    endpoints: {
      login: '/rest/auth/angelbroking/user/v1/loginByPassword',
      profile: '/rest/secure/angelbroking/user/v1/getProfile',
      historicalData: '/rest/secure/angelbroking/historical/v1/getCandleData',
      ltp: '/rest/secure/angelbroking/order/v1/getLtpData',
      logout: '/rest/secure/angelbroking/user/v1/logout',
    },
  },
  rateLimit: {
    requestsPerSecond: 10,
    requestsPerMinute: 100,
  },
} as const;

export const MARKET_CONFIG = {
  exchanges: ['NSE', 'BSE'] as const,
  segments: ['EQUITY'] as const, // Excluding OPTIONS as per requirement
  tradingHours: {
    start: '09:15',
    end: '15:30',
    timezone: 'Asia/Kolkata',
  },
  holidays: [], // To be populated with market holidays
} as const;

export const BENCHMARK_CONFIG = {
  types: {
    GOLD: {
      name: 'Gold',
      symbol: 'GOLD',
      description: 'Gold prices in INR per 10 grams',
    },
    FD: {
      name: 'Fixed Deposit',
      symbol: 'FD',
      description: 'Average FD rates from major banks',
      defaultRate: 6.5, // Default FD rate percentage
    },
    NIFTY: {
      name: 'Nifty 50',
      symbol: 'NIFTY',
      description: 'NSE Nifty 50 Index',
      token: '********', // Angel One token for Nifty 50
    },
  },
} as const;

export const CALCULATION_CONFIG = {
  precision: {
    currency: 2,
    percentage: 2,
    cagr: 2,
  },
  defaults: {
    fdRate: 6.5, // Default FD rate percentage
    inflationRate: 6.0, // Default inflation rate
  },
} as const;

export const UI_CONFIG = {
  theme: {
    primary: '#1f2937',
    secondary: '#374151',
    accent: '#3b82f6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  },
  charts: {
    defaultHeight: 400,
    colors: {
      investment: '#3b82f6',
      gold: '#f59e0b',
      fd: '#10b981',
      nifty: '#8b5cf6',
    },
  },
} as const;

export const STORAGE_CONFIG = {
  keys: {
    userPreferences: 'whatif_user_preferences',
    savedScenarios: 'whatif_saved_scenarios',
    apiCredentials: 'whatif_api_credentials',
  },
  encryption: {
    enabled: true,
    algorithm: 'AES-256-GCM',
  },
} as const;

// Environment-specific configuration
export const getEnvironmentConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    isDevelopment,
    isProduction,
    apiUrl: process.env.NEXT_PUBLIC_API_URL || API_CONFIG.angelOne.baseUrl,
    enableLogging: isDevelopment,
    enableAnalytics: isProduction,
  };
};
