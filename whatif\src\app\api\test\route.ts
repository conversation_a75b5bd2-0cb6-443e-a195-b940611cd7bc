import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Test API route called');
    
    // Test environment variables
    const envVars = {
      ANGEL_ONE_API_KEY: process.env.ANGEL_ONE_API_KEY ? 'SET' : 'MISSING',
      ANGEL_ONE_CLIENT_ID: process.env.ANGEL_ONE_CLIENT_ID ? 'SET' : 'MISSING',
      ANGEL_ONE_PASSWORD: process.env.ANGEL_ONE_PASSWORD ? 'SET' : 'MISSING',
      ANGEL_ONE_TOTP_SECRET: process.env.ANGEL_ONE_TOTP_SECRET ? 'SET' : 'MISSING',
    };

    console.log('🔧 Environment variables:', envVars);

    return NextResponse.json({
      success: true,
      message: 'Test API route working',
      timestamp: new Date().toISOString(),
      environment: envVars,
    });
  } catch (error) {
    console.error('❌ Test API route error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Test API POST route called');
    const body = await request.json();
    console.log('📥 Request body:', body);

    return NextResponse.json({
      success: true,
      message: 'Test POST API route working',
      receivedData: body,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Test POST API route error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
